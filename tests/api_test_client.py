"""
API Test Client for testing API endpoints consistently.
"""
import json
from django.test import Client
from django.contrib.auth import get_user_model
from django.urls import reverse
from django.http import HttpResponse

# HTTP status codes (since we don't have rest_framework)
class status:
    HTTP_200_OK = 200
    HTTP_201_CREATED = 201
    HTTP_204_NO_CONTENT = 204
    HTTP_400_BAD_REQUEST = 400
    HTTP_401_UNAUTHORIZED = 401
    HTTP_403_FORBIDDEN = 403
    HTTP_404_NOT_FOUND = 404
    HTTP_500_INTERNAL_SERVER_ERROR = 500

User = get_user_model()


class APITestClient:
    """Enhanced test client for API testing with authentication and response validation."""
    
    def __init__(self):
        self.client = Client()
        self.user = None
    
    def authenticate(self, user):
        """Authenticate the client with a user."""
        self.user = user
        self.client.force_login(user)
    
    def logout(self):
        """Logout the current user."""
        self.client.logout()
        self.user = None
    
    def get(self, url, data=None, **extra):
        """Make a GET request."""
        return self.client.get(url, data=data, **extra)
    
    def post(self, url, data=None, content_type='application/json', **extra):
        """Make a POST request."""
        if content_type == 'application/json' and data:
            data = json.dumps(data)
        return self.client.post(url, data=data, content_type=content_type, **extra)
    
    def put(self, url, data=None, content_type='application/json', **extra):
        """Make a PUT request."""
        if content_type == 'application/json' and data:
            data = json.dumps(data)
        return self.client.put(url, data=data, content_type=content_type, **extra)
    
    def patch(self, url, data=None, content_type='application/json', **extra):
        """Make a PATCH request."""
        if content_type == 'application/json' and data:
            data = json.dumps(data)
        return self.client.patch(url, data=data, content_type=content_type, **extra)
    
    def delete(self, url, **extra):
        """Make a DELETE request."""
        return self.client.delete(url, **extra)
    
    def assert_response_format(self, response, expected_status=status.HTTP_200_OK):
        """Assert that the response follows the expected API format."""
        if response.status_code != expected_status:
            raise AssertionError(f"Expected status {expected_status}, got {response.status_code}")
        
        # Check if response is JSON
        try:
            # Handle both Django test client response and actual HTTP response
            if hasattr(response, 'json'):
                data = response.json()
            else:
                # Django test client response
                data = json.loads(response.content.decode('utf-8'))
        except (ValueError, json.JSONDecodeError):
            raise AssertionError("Response is not valid JSON")
        
        # Check for required fields in API response
        if expected_status >= 400:
            # Error response format
            if 'status' not in data:
                raise AssertionError("Error response missing 'status' field")
            if data['status'] != 'error':
                raise AssertionError(f"Expected status 'error', got '{data['status']}'")
            if 'error' not in data:
                raise AssertionError("Error response missing 'error' field")
            if 'message' not in data['error']:
                raise AssertionError("Error response missing 'message' field")
        else:
            # Success response format
            if 'status' not in data:
                raise AssertionError("Success response missing 'status' field")
            if data['status'] != 'success':
                raise AssertionError(f"Expected status 'success', got '{data['status']}'")
        
        return data
    
    def assert_success_response(self, response, expected_data_keys=None):
        """Assert successful API response and optionally check data keys."""
        data = self.assert_response_format(response, status.HTTP_200_OK)
        
        if expected_data_keys:
            self.assertIn('data', data)
            for key in expected_data_keys:
                self.assertIn(key, data['data'])
        
        return data
    
    def assert_error_response(self, response, expected_status, expected_error_code=None):
        """Assert error API response format."""
        data = self.assert_response_format(response, expected_status)
        
        if expected_error_code:
            self.assertIn('code', data['error'])
            self.assertEqual(data['error']['code'], expected_error_code)
        
        return data
    
    def test_endpoint(self, endpoint, method='GET', data=None, expected_status=status.HTTP_200_OK, 
                     auth_required=True, expected_data_keys=None):
        """
        Test an endpoint with comprehensive validation.
        
        Args:
            endpoint: URL endpoint to test
            method: HTTP method (GET, POST, PUT, PATCH, DELETE)
            data: Request data
            expected_status: Expected HTTP status code
            auth_required: Whether authentication is required
            expected_data_keys: List of keys expected in response data
        
        Returns:
            TestResult object with response and validation results
        """
        # Test without authentication if required
        if auth_required and not self.user:
            response = getattr(self.client, method.lower())(endpoint, data=data)
            if response.status_code in [401, 403]:
                auth_test_passed = True
            else:
                auth_test_passed = False
        else:
            auth_test_passed = True
        
        # Test with authentication
        if self.user:
            response = getattr(self, method.lower())(endpoint, data=data)
        else:
            response = getattr(self.client, method.lower())(endpoint, data=data)
        
        # Validate response format
        try:
            if expected_status >= 400:
                response_data = self.assert_error_response(response, expected_status)
            else:
                response_data = self.assert_success_response(response, expected_data_keys)
            format_valid = True
        except AssertionError as e:
            format_valid = False
            response_data = None
        
        return TestResult(
            response=response,
            data=response_data,
            auth_test_passed=auth_test_passed,
            format_valid=format_valid,
            status_code=response.status_code
        )


class TestResult:
    """Container for test results."""
    
    def __init__(self, response, data, auth_test_passed, format_valid, status_code):
        self.response = response
        self.data = data
        self.auth_test_passed = auth_test_passed
        self.format_valid = format_valid
        self.status_code = status_code
    
    def is_successful(self):
        """Check if all tests passed."""
        return self.auth_test_passed and self.format_valid and self.status_code < 400


class APIEndpointTester:
    """Helper class for testing multiple API endpoints systematically."""
    
    def __init__(self, test_case, api_client):
        self.test_case = test_case
        self.api_client = api_client
    
    def test_crud_endpoints(self, base_url, model_data, auth_user=None):
        """Test CRUD operations for a resource."""
        if auth_user:
            self.api_client.authenticate(auth_user)
        
        results = {}
        
        # Test CREATE (POST)
        create_result = self.api_client.test_endpoint(
            base_url,
            method='POST',
            data=model_data,
            expected_status=status.HTTP_201_CREATED
        )
        results['create'] = create_result
        
        # If create was successful, test other operations
        if create_result.is_successful() and create_result.data:
            resource_id = create_result.data.get('data', {}).get('id')
            if resource_id:
                detail_url = f"{base_url}{resource_id}/"
                
                # Test READ (GET)
                results['read'] = self.api_client.test_endpoint(detail_url)
                
                # Test UPDATE (PUT)
                update_data = model_data.copy()
                update_data['updated_field'] = 'updated_value'
                results['update'] = self.api_client.test_endpoint(
                    detail_url,
                    method='PUT',
                    data=update_data
                )
                
                # Test DELETE
                results['delete'] = self.api_client.test_endpoint(
                    detail_url,
                    method='DELETE',
                    expected_status=status.HTTP_204_NO_CONTENT
                )
        
        # Test LIST (GET)
        results['list'] = self.api_client.test_endpoint(base_url)
        
        return results
    
    def test_authentication_required(self, endpoints):
        """Test that endpoints require authentication."""
        # Logout to test without auth
        self.api_client.logout()
        
        results = {}
        for endpoint in endpoints:
            result = self.api_client.test_endpoint(
                endpoint,
                expected_status=status.HTTP_401_UNAUTHORIZED,
                auth_required=True
            )
            results[endpoint] = result
        
        return results