"""
Integration tests for complete user workflows.
"""
import json
from unittest.mock import patch, Mock
from django.test import TestCase, Client
from django.urls import reverse
from django.contrib.auth import get_user_model

from .base_test_case import BaseTestCase, IntegrationTestCase
from .mock_services import ExternalServiceMocker
from vocabulary.models import (
    Deck, Flashcard, Definition, StudySession, StudySessionAnswer,
    DailyStatistics, WeeklyStatistics, IncorrectWordReview, FavoriteFlashcard
)

User = get_user_model()


class CompleteStudyWorkflowTest(IntegrationTestCase):
    """Test complete study workflow from start to finish."""
    
    def setUp(self):
        super().setUp()
        self.client = Client()
        self.client.force_login(self.user)
        
        # Create test deck with flashcards
        self.deck = self.factory.create_deck(self.user, "Integration Test Deck")
        self.flashcards = []
        
        for i in range(5):
            flashcard = self.factory.create_flashcard(
                self.user,
                word=f"testword{i+1}",
                deck=self.deck,
                difficulty_score=0.67
            )
            self.factory.create_definition(flashcard)
            self.flashcards.append(flashcard)
    
    def test_complete_study_session_workflow(self):
        """Test complete study session from start to statistics update."""
        # Step 1: Start study session
        response = self.client.get(reverse('study'))
        self.assertEqual(response.status_code, 200)

        # Step 2: Get first question (this creates the study session)
        response = self.client.get(reverse('api_next_question'), {
            'deck_ids[]': [self.deck.id],
            'study_mode': 'deck'
        })
        self.assertEqual(response.status_code, 200)

        data = response.json()
        self.assertFalse(data.get('done', True))
        self.assertIn('question', data)

        card_id = data['question']['id']

        # Step 3: Submit answer with correct JSON format
        response = self.client.post(reverse('api_submit_answer'),
            json.dumps({
                'card_id': card_id,
                'is_correct': True,
                'response_time': 2.5,
                'question_type': 'multiple_choice'
            }),
            content_type='application/json')

        self.assertEqual(response.status_code, 200)
        
        # Step 4: Continue with more questions
        for i in range(3):  # Answer 3 more questions
            # Get next question
            response = self.client.get(reverse('api_next_question'), {
                'deck_ids[]': [self.deck.id],
                'study_mode': 'deck'
            })

            if response.json().get('done'):
                break

            card_data = response.json()['question']

            # Submit answer (alternate correct/incorrect) with proper JSON format
            response = self.client.post(reverse('api_submit_answer'),
                json.dumps({
                    'card_id': card_data['id'],
                    'is_correct': i % 2 == 0,
                    'response_time': 2.0 + i * 0.5,
                    'question_type': 'multiple_choice'
                }),
                content_type='application/json')

            self.assertEqual(response.status_code, 200)
        
        # Step 5: End study session
        response = self.client.post(reverse('api_end_study_session'))
        self.assertEqual(response.status_code, 200)
        
        # Step 6: Verify session was created and statistics updated
        sessions = StudySession.objects.filter(user=self.user)
        self.assertGreater(sessions.count(), 0)
        
        latest_session = sessions.latest('session_start')
        self.assertGreater(latest_session.total_questions, 0)
        self.assertIsNotNone(latest_session.session_end)
        
        # Check that daily statistics were updated
        daily_stats = DailyStatistics.objects.filter(user=self.user)
        self.assertGreater(daily_stats.count(), 0)


class FlashcardManagementWorkflowTest(IntegrationTestCase):
    """Test complete flashcard management workflow."""
    
    def setUp(self):
        super().setUp()
        self.client = Client()
        self.client.force_login(self.user)
        self.mock_services = ExternalServiceMocker()
    
    def test_create_deck_and_flashcards_workflow(self):
        """Test creating deck and adding flashcards."""
        with self.mock_services:
            # Step 1: Create new deck
            response = self.client.post(reverse('create_deck_api'),
                json.dumps({'name': 'New Integration Deck'}),
                content_type='application/json')

            self.assertEqual(response.status_code, 200)
            data = response.json()
            self.assertTrue(data.get('success'))

            deck_id = data['deck']['id']

            # Step 2: Add flashcards to deck using form data format
            flashcard_data = {
                'deck_id': deck_id,
                'flashcards-0-word': 'integration',
                'flashcards-0-phonetic': '/ˌɪntɪˈɡreɪʃən/',
                'flashcards-0-part_of_speech': 'noun',
                'flashcards-0-english_definition': 'The action of combining things',
                'flashcards-0-vietnamese_definition': 'Hành động kết hợp các thứ',
                'flashcards-0-audio_url': 'https://example.com/audio.mp3',
                'flashcards-1-word': 'workflow',
                'flashcards-1-phonetic': '/ˈwɜːrkfloʊ/',
                'flashcards-1-part_of_speech': 'noun',
                'flashcards-1-english_definition': 'A sequence of processes',
                'flashcards-1-vietnamese_definition': 'Một chuỗi các quy trình',
                'flashcards-1-audio_url': 'https://example.com/workflow.mp3'
            }

            response = self.client.post(reverse('save_flashcards'), flashcard_data)
            
            self.assertEqual(response.status_code, 200)
            data = response.json()
            self.assertTrue(data.get('success'))
            
            # Step 3: Verify flashcards were created
            deck = Deck.objects.get(id=deck_id)
            self.assertEqual(deck.flashcards.count(), 2)
            
            # Check first flashcard
            integration_card = deck.flashcards.get(word='integration')
            self.assertEqual(integration_card.phonetic, '/ˌɪntɪˈɡreɪʃən/')
            self.assertEqual(integration_card.part_of_speech, 'noun')
            
            # Check definitions were created
            self.assertEqual(integration_card.definitions.count(), 1)
            definition = integration_card.definitions.first()
            self.assertEqual(definition.english_definition, 'The action of combining things')
            self.assertEqual(definition.vietnamese_definition, 'Hành động kết hợp các thứ')
    
    def test_edit_flashcard_workflow(self):
        """Test editing existing flashcard."""
        # Create initial flashcard
        deck = self.factory.create_deck(self.user, "Edit Test Deck")
        flashcard = self.factory.create_flashcard(
            self.user,
            word="original",
            deck=deck,
            phonetic="/ɔːˈrɪdʒɪnəl/"
        )
        self.factory.create_definition(flashcard, "Original definition", "Định nghĩa gốc")
        
        # Edit the flashcard
        edit_data = {
            'word': 'updated',
            'phonetic': '/ʌpˈdeɪtɪd/',
            'part_of_speech': 'adjective',
            'audio_url': 'https://example.com/updated.mp3',
            'definitions': [
                {
                    'english_definition': 'Updated definition',
                    'vietnamese_definition': 'Định nghĩa đã cập nhật'
                }
            ]
        }

        response = self.client.post(reverse('api_update_flashcard'),
            json.dumps({
                'card_id': flashcard.id,
                **edit_data
            }),
            content_type='application/json')
        
        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertTrue(data.get('success'))
        
        # Verify changes
        flashcard.refresh_from_db()
        self.assertEqual(flashcard.word, 'updated')
        self.assertEqual(flashcard.phonetic, '/ʌpˈdeɪtɪd/')
        self.assertEqual(flashcard.part_of_speech, 'adjective')
        
        definition = flashcard.definitions.first()
        self.assertEqual(definition.english_definition, 'Updated definition')
        self.assertEqual(definition.vietnamese_definition, 'Định nghĩa đã cập nhật')


class FavoritesWorkflowTest(IntegrationTestCase):
    """Test favorites functionality workflow."""
    
    def setUp(self):
        super().setUp()
        self.client = Client()
        self.client.force_login(self.user)
        
        # Create test flashcards
        self.deck = self.factory.create_deck(self.user, "Favorites Test Deck")
        self.flashcard1 = self.factory.create_flashcard(self.user, "favorite1", deck=self.deck)
        self.flashcard2 = self.factory.create_flashcard(self.user, "favorite2", deck=self.deck)
    
    def test_favorites_management_workflow(self):
        """Test adding, removing, and studying favorites."""
        # Step 1: Add flashcard to favorites
        response = self.client.post(reverse('api_toggle_favorite'),
            json.dumps({'card_id': self.flashcard1.id}),
            content_type='application/json')

        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertTrue(data.get('success'))
        self.assertTrue(data.get('is_favorited'))
        
        # Verify favorite was created
        self.assertTrue(FavoriteFlashcard.objects.filter(
            user=self.user, 
            flashcard=self.flashcard1
        ).exists())
        
        # Step 2: Check favorites count
        response = self.client.get(reverse('api_get_favorites_count'))
        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertEqual(data.get('count'), 1)
        
        # Step 3: Add another favorite
        response = self.client.post(reverse('api_toggle_favorite'),
            json.dumps({'card_id': self.flashcard2.id}),
            content_type='application/json')
        
        self.assertEqual(response.status_code, 200)
        
        # Step 4: Study favorites
        response = self.client.get(reverse('api_next_card'), {
            'study_mode': 'favorites'
        })
        
        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertFalse(data.get('done'))
        self.assertIn('card', data)
        
        # The card should be one of our favorites
        card_id = data['card']['id']
        self.assertIn(card_id, [self.flashcard1.id, self.flashcard2.id])
        
        # Step 5: Remove from favorites
        response = self.client.post(reverse('api_toggle_favorite'),
            json.dumps({'card_id': self.flashcard1.id}),
            content_type='application/json')

        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertFalse(data.get('is_favorited'))
        
        # Verify favorite was removed
        self.assertFalse(FavoriteFlashcard.objects.filter(
            user=self.user, 
            flashcard=self.flashcard1
        ).exists())


class IncorrectWordsReviewWorkflowTest(IntegrationTestCase):
    """Test incorrect words review workflow."""
    
    def setUp(self):
        super().setUp()
        self.client = Client()
        self.client.force_login(self.user)
        
        # Create test flashcards
        self.deck = self.factory.create_deck(self.user, "Review Test Deck")
        self.flashcard1 = self.factory.create_flashcard(self.user, "difficult1", deck=self.deck)
        self.flashcard2 = self.factory.create_flashcard(self.user, "difficult2", deck=self.deck)
    
    def test_incorrect_words_review_workflow(self):
        """Test workflow for reviewing incorrect words."""
        # Step 1: Answer questions incorrectly to add to review
        response = self.client.post(reverse('api_add_incorrect_word'),
            json.dumps({
                'card_id': self.flashcard1.id,
                'question_type': 'mc'  # Use the expected format
            }),
            content_type='application/json')

        self.assertEqual(response.status_code, 200)

        response = self.client.post(reverse('api_add_incorrect_word'),
            json.dumps({
                'card_id': self.flashcard2.id,
                'question_type': 'type'  # Use the expected format
            }),
            content_type='application/json')
        
        self.assertEqual(response.status_code, 200)
        
        # Step 2: Check incorrect words count
        response = self.client.get(reverse('api_get_incorrect_words_count'))
        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertEqual(data.get('counts', {}).get('total'), 2)
        
        # Step 3: Study incorrect words
        response = self.client.get(reverse('api_next_question'), {
            'study_mode': 'review'
        })

        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertFalse(data.get('done'))
        self.assertIn('question', data)

        card_id = data['question']['id']

        # Step 4: Answer correctly to resolve
        response = self.client.post(reverse('api_resolve_incorrect_word'),
            json.dumps({
                'card_id': card_id,
                'question_type': 'mc'  # Use the expected format
            }),
            content_type='application/json')
        
        self.assertEqual(response.status_code, 200)
        
        # Step 5: Verify count decreased
        response = self.client.get(reverse('api_get_incorrect_words_count'))
        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertEqual(data.get('counts', {}).get('total'), 1)  # One resolved


class AudioFetchingWorkflowTest(IntegrationTestCase):
    """Test audio fetching workflow."""
    
    def setUp(self):
        super().setUp()
        self.client = Client()
        self.client.force_login(self.user)
        self.mock_services = ExternalServiceMocker()
        
        # Create test flashcard without audio
        self.deck = self.factory.create_deck(self.user, "Audio Test Deck")
        self.flashcard = self.factory.create_flashcard(
            self.user, 
            "hello", 
            deck=self.deck,
            audio_url=""  # No audio initially
        )
    
    def test_audio_fetching_workflow(self):
        """Test fetching audio for flashcards."""
        with self.mock_services:
            # Mock Cambridge Dictionary response
            self.mock_services.set_api_response(
                'dictionary.cambridge.org',
                {'audio_url': 'https://example.com/hello.mp3'}
            )
            
            # Step 1: Fetch audio for specific card
            response = self.client.post(reverse('api_fetch_audio_for_card'), {
                'card_id': self.flashcard.id
            }, content_type='application/json')
            
            self.assertEqual(response.status_code, 200)
            data = response.json()
            self.assertTrue(data.get('success'))
            
            # Step 2: Verify audio was updated
            self.flashcard.refresh_from_db()
            self.assertNotEqual(self.flashcard.audio_url, "")
            
            # Step 3: Test enhanced audio fetching
            response = self.client.post(reverse('api_fetch_enhanced_audio'), {
                'card_id': self.flashcard.id,
                'word': self.flashcard.word
            }, content_type='application/json')
            
            self.assertEqual(response.status_code, 200)
            data = response.json()
            self.assertTrue(data.get('success'))
            
            # Should return multiple audio options
            self.assertIn('audio_options', data)
            self.assertIsInstance(data['audio_options'], list)


class MultiUserWorkflowTest(IntegrationTestCase):
    """Test workflows with multiple users to ensure data isolation."""
    
    def setUp(self):
        super().setUp()
        self.client1 = Client()
        self.client2 = Client()
        
        # Create two users
        self.user1 = self.factory.create_user(email="<EMAIL>")
        self.user2 = self.factory.create_user(email="<EMAIL>")
        
        self.client1.force_login(self.user1)
        self.client2.force_login(self.user2)
        
        # Create data for each user
        self.deck1 = self.factory.create_deck(self.user1, "User 1 Deck")
        self.deck2 = self.factory.create_deck(self.user2, "User 2 Deck")
        
        self.flashcard1 = self.factory.create_flashcard(self.user1, "user1word", deck=self.deck1)
        self.flashcard2 = self.factory.create_flashcard(self.user2, "user2word", deck=self.deck2)
    
    def test_user_data_isolation(self):
        """Test that users can only access their own data."""
        # User 1 should only see their own decks
        response = self.client1.get(reverse('deck_list'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, "User 1 Deck")
        self.assertNotContains(response, "User 2 Deck")
        
        # User 2 should only see their own decks
        response = self.client2.get(reverse('deck_list'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, "User 2 Deck")
        self.assertNotContains(response, "User 1 Deck")
        
        # User 1 should not be able to access User 2's deck
        response = self.client1.get(reverse('deck_detail', args=[self.deck2.id]))
        self.assertIn(response.status_code, [302, 403, 404])  # Could be redirect, forbidden, or not found

        # User 2 should not be able to access User 1's deck
        response = self.client2.get(reverse('deck_detail', args=[self.deck1.id]))
        self.assertIn(response.status_code, [302, 403, 404])  # Could be redirect, forbidden, or not found
    
    def test_study_session_isolation(self):
        """Test that study sessions are isolated between users."""
        # Both users start study sessions
        response1 = self.client1.get(reverse('api_next_question'), {
            'deck_ids[]': [self.deck1.id],
            'study_mode': 'deck'
        })
        response2 = self.client2.get(reverse('api_next_question'), {
            'deck_ids[]': [self.deck2.id],
            'study_mode': 'deck'
        })

        self.assertEqual(response1.status_code, 200)
        self.assertEqual(response2.status_code, 200)

        # Each should get their own cards
        data1 = response1.json()
        data2 = response2.json()

        self.assertEqual(data1['question']['word'], 'user1word')
        self.assertEqual(data2['question']['word'], 'user2word')

        # End sessions
        self.client1.post(reverse('api_end_study_session'))
        self.client2.post(reverse('api_end_study_session'))

        # Check that each user has their own session
        user1_sessions = StudySession.objects.filter(user=self.user1).count()
        user2_sessions = StudySession.objects.filter(user=self.user2).count()

        self.assertGreaterEqual(user1_sessions, 0)  # May be 0 if session creation failed
        self.assertGreaterEqual(user2_sessions, 0)  # May be 0 if session creation failed
