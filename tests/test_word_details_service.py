"""
Comprehensive tests for word details service.
"""
import json
from unittest.mock import patch, <PERSON><PERSON>
from django.test import TestCase

from .base_test_case import BaseTestCase
from .mock_services import ExternalServiceMock<PERSON>, MockDatamuseAPI
from vocabulary.word_details_service import get_word_details


class WordDetailsServiceTest(BaseTestCase):
    """Test word details service functions."""

    @patch('vocabulary.word_details_service.requests.get')
    def test_get_word_details_success(self, mock_get):
        """Test successful word details retrieval."""
        # Mock Dictionary API response
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = [
            {
                "word": "test",
                "phonetics": [
                    {
                        "text": "/test/",
                        "audio": "https://example.com/test.mp3"
                    }
                ],
                "meanings": [
                    {
                        "partOfSpeech": "noun",
                        "definitions": [
                            {
                                "definition": "a procedure intended to establish the quality",
                                "example": "a test of the new system"
                            }
                        ]
                    }
                ]
            }
        ]
        mock_get.return_value = mock_response

        # Test the function
        result = get_word_details("test")

        # Check result structure
        self.assertIsInstance(result, dict)

        # Should not have error
        self.assertNotIn('error', result)

        # Should have phonetics and meanings
        if 'phonetics' in result:
            self.assertIsInstance(result['phonetics'], list)
        if 'meanings' in result:
            self.assertIsInstance(result['meanings'], list)
    
    @patch('vocabulary.word_details_service.requests.get')
    def test_get_word_details_word_not_found(self, mock_get):
        """Test word details when word is not found."""
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = []  # Empty response
        mock_get.return_value = mock_response

        result = get_word_details("nonexistentword")

        self.assertIsInstance(result, dict)
        self.assertIn('error', result)

    @patch('vocabulary.word_details_service.requests.get')
    def test_get_word_details_api_error(self, mock_get):
        """Test word details with API error."""
        mock_get.side_effect = Exception("Network error")

        result = get_word_details("test")

        self.assertIsInstance(result, dict)
        self.assertIn('error', result)


class WordDetailsServiceIntegrationTest(BaseTestCase):
    """Integration tests for word details service."""

    def setUp(self):
        super().setUp()
        self.mock_services = ExternalServiceMocker()

    def test_complete_word_details_workflow(self):
        """Test complete workflow with mocked external services."""
        with self.mock_services:
            # Set up mock responses
            self.mock_services.set_api_response(
                'api.dictionaryapi.dev',
                [
                    {
                        "word": "test",
                        "phonetics": [{"text": "/test/", "audio": ""}],
                        "meanings": [
                            {
                                "partOfSpeech": "noun",
                                "definitions": [
                                    {"definition": "a procedure", "example": ""}
                                ]
                            }
                        ]
                    }
                ]
            )

            result = get_word_details("test")

            self.assertIsInstance(result, dict)
            self.assertNotIn('error', result)

    def test_word_details_with_api_failure(self):
        """Test word details handling when API fails."""
        with self.mock_services:
            # Set up API error
            self.mock_services.set_api_error('api.dictionaryapi.dev', 500)

            result = get_word_details("test")

            self.assertIsInstance(result, dict)
            # May or may not have error depending on implementation



