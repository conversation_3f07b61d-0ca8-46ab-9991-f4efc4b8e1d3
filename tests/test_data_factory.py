"""
Test Data Factory for generating test data consistently across tests.
"""
from django.contrib.auth import get_user_model
from django.utils import timezone
from datetime import date, timedelta
from vocabulary.models import Deck, Flashcard, Definition, StudySession
import random

User = get_user_model()


class TestDataFactory:
    """Factory class for creating test data objects."""
    
    @staticmethod
    def create_user(email="<EMAIL>", password="testpass123"):
        """Create a test user."""
        return User.objects.create_user(
            email=email,
            password=password
        )
    
    @staticmethod
    def create_deck(user, name="Test Deck"):
        """Create a test deck for a user."""
        return Deck.objects.create(
            user=user,
            name=name
        )
    
    @staticmethod
    def create_flashcard(user, word="test", deck=None, **kwargs):
        """Create a test flashcard."""
        defaults = {
            'user': user,
            'word': word,
            'phonetic': '/test/',
            'part_of_speech': 'noun',
            'general_synonyms': 'example, sample',
            'general_antonyms': 'real, actual',
            'difficulty_score': 0.67,
            'total_reviews': 5,
            'correct_reviews': 3,
            'times_seen_today': 1,
            'last_seen_date': date.today(),
        }
        defaults.update(kwargs)
        
        if deck:
            defaults['deck'] = deck
            
        return Flashcard.objects.create(**defaults)
    
    @staticmethod
    def create_definition(flashcard, english_def="Test definition", vietnamese_def="Định nghĩa thử nghiệm"):
        """Create a test definition for a flashcard."""
        return Definition.objects.create(
            flashcard=flashcard,
            english_definition=english_def,
            vietnamese_definition=vietnamese_def,
            definition_synonyms="test, example",
            definition_antonyms="real, actual"
        )
    
    @staticmethod
    def create_study_session(user, study_mode='deck', **kwargs):
        """Create a test study session."""
        defaults = {
            'user': user,
            'study_mode': study_mode,
            'total_questions': 10,
            'correct_answers': 7,
            'incorrect_answers': 3,
            'session_duration_seconds': 300,
            'words_studied': 8,
            'average_response_time': 30.0,
        }
        defaults.update(kwargs)
        
        return StudySession.objects.create(**defaults)
    
    @staticmethod
    def create_user_with_flashcards(count=5, email="<EMAIL>"):
        """Create a user with a specified number of flashcards."""
        user = TestDataFactory.create_user(email=email)
        deck = TestDataFactory.create_deck(user, "Test Deck")
        
        flashcards = []
        for i in range(count):
            flashcard = TestDataFactory.create_flashcard(
                user=user,
                word=f"word{i+1}",
                deck=deck,
                difficulty_score=random.choice([0.0, 0.33, 0.67, 1.0])
            )
            TestDataFactory.create_definition(flashcard)
            flashcards.append(flashcard)
        
        return user, flashcards
    
    @staticmethod
    def create_study_session_data(user=None):
        """Create comprehensive study session test data."""
        if not user:
            user = TestDataFactory.create_user()
        
        # Create decks and flashcards
        deck1 = TestDataFactory.create_deck(user, "Deck 1")
        deck2 = TestDataFactory.create_deck(user, "Deck 2")
        
        flashcards = []
        for i in range(10):
            deck = deck1 if i < 5 else deck2
            flashcard = TestDataFactory.create_flashcard(
                user=user,
                word=f"testword{i+1}",
                deck=deck
            )
            TestDataFactory.create_definition(flashcard)
            flashcards.append(flashcard)
        
        # Create study sessions
        session1 = TestDataFactory.create_study_session(
            user=user,
            study_mode='deck',
            total_questions=5,
            correct_answers=4
        )
        session1.decks_studied.add(deck1)
        
        session2 = TestDataFactory.create_study_session(
            user=user,
            study_mode='random',
            total_questions=8,
            correct_answers=6
        )
        session2.decks_studied.add(deck1, deck2)
        
        return {
            'user': user,
            'decks': [deck1, deck2],
            'flashcards': flashcards,
            'sessions': [session1, session2]
        }
    
    @staticmethod
    def create_spaced_repetition_cards(user, count=10):
        """Create flashcards with various spaced repetition states."""
        deck = TestDataFactory.create_deck(user, "Spaced Repetition Deck")
        cards = []
        
        # Create cards with different difficulty levels and review dates
        difficulty_levels = [0.0, 0.33, 0.67, 1.0]
        
        for i in range(count):
            # Vary the last seen date to test spaced repetition logic
            days_ago = random.randint(0, 7)
            last_seen = date.today() - timedelta(days=days_ago)
            
            card = TestDataFactory.create_flashcard(
                user=user,
                word=f"spaced_word_{i+1}",
                deck=deck,
                difficulty_score=random.choice(difficulty_levels),
                last_seen_date=last_seen,
                times_seen_today=random.randint(0, 3),
                total_reviews=random.randint(1, 20),
                correct_reviews=random.randint(0, 15)
            )
            TestDataFactory.create_definition(card)
            cards.append(card)
        
        return cards