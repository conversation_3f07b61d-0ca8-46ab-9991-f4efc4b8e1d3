"""
Example tests demonstrating the testing framework usage.
"""
from .base_test_case import BaseTestCase, APITestCase, ModelTestCase, PerformanceTestCase
from .mock_services import mock_external_apis
from vocabulary.models import Flashcard, Deck, Definition


class TestDataFactoryExample(BaseTestCase):
    """Example tests for TestDataFactory."""
    
    def test_create_user(self):
        """Test creating a user with factory."""
        user = self.factory.create_user(email="<EMAIL>")
        self.assertEqual(user.email, "<EMAIL>")
        self.assertTrue(user.check_password("testpass123"))
    
    def test_create_flashcard(self):
        """Test creating a flashcard with factory."""
        flashcard = self.factory.create_flashcard(
            user=self.user,
            word="example",
            difficulty_score=0.67
        )
        self.assertEqual(flashcard.word, "example")
        self.assertEqual(flashcard.difficulty_score, 0.67)
        self.assertEqual(flashcard.user, self.user)
    
    def test_create_user_with_flashcards(self):
        """Test creating user with multiple flashcards."""
        user, flashcards = self.factory.create_user_with_flashcards(count=3)
        self.assertEqual(len(flashcards), 3)
        self.assertEqual(user.flashcards.count(), 3)
        
        # Check that each flashcard has a definition
        for flashcard in flashcards:
            self.assertTrue(flashcard.definitions.exists())


class APITestClientExample(APITestCase):
    """Example tests for APITestClient."""
    
    def test_api_response_format(self):
        """Test API response format validation."""
        # This would test an actual API endpoint
        # For now, we'll create a mock response
        from django.http import JsonResponse
        from django.test import RequestFactory
        
        # Simulate an API response
        response_data = {
            'status': 'success',
            'data': {'message': 'Hello World'}
        }
        response = JsonResponse(response_data)
        
        # Test response format validation
        data = self.api_client.assert_response_format(response, 200)
        self.assertEqual(data['status'], 'success')
        self.assertIn('data', data)
    
    def test_authentication_required(self):
        """Test that authentication is properly checked."""
        # Logout to test without authentication
        self.api_client.logout()
        
        # This would test actual endpoints that require auth
        # For demonstration, we'll just check that we're logged out
        self.assertIsNone(self.api_client.user)


class MockServicesExample(BaseTestCase):
    """Example tests for mock services."""
    
    @mock_external_apis
    def test_external_api_mocking(self):
        """Test that external APIs are properly mocked."""
        import requests
        
        # Test Datamuse API mock
        response = requests.get('https://api.datamuse.com/words?ml=test')
        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertIsInstance(data, list)
        self.assertTrue(len(data) > 0)
    
    def test_mock_service_context_manager(self):
        """Test using mock services as context manager."""
        from .mock_services import ExternalServiceMocker
        import requests
        
        with ExternalServiceMocker() as mocker:
            # Test that APIs are mocked within context
            response = requests.get('https://api.datamuse.com/words?ml=test')
            self.assertEqual(response.status_code, 200)
            
            # Test custom response
            mocker.set_api_response(
                'custom-api.com',
                {'custom': 'response'},
                status_code=201
            )
            
            custom_response = requests.get('https://custom-api.com/test')
            self.assertEqual(custom_response.status_code, 201)
            self.assertEqual(custom_response.json()['custom'], 'response')


class ModelTestExample(ModelTestCase):
    """Example tests for model testing."""
    
    def test_flashcard_model_properties(self):
        """Test flashcard model properties."""
        flashcard = self.factory.create_flashcard(
            user=self.user,
            word="test",
            difficulty_score=0.67,
            total_reviews=10,
            correct_reviews=7
        )
        
        # Test difficulty level property
        self.assert_model_property(flashcard, 'difficulty_level', 'Good')
        
        # Test accuracy percentage property
        self.assert_model_property(flashcard, 'accuracy_percentage', 70.0)
        
        # Test string representation
        self.assert_model_str_representation(flashcard, "test")
    
    def test_model_validation(self):
        """Test model field validation."""
        # Test that duplicate words for same user are not allowed
        self.factory.create_flashcard(user=self.user, word="duplicate")
        
        # This should raise a validation error
        with self.assertRaises(Exception):  # IntegrityError or ValidationError
            self.factory.create_flashcard(user=self.user, word="duplicate")
    
    def test_model_relationships(self):
        """Test model relationships."""
        deck = self.factory.create_deck(user=self.user, name="Test Deck")
        flashcard = self.factory.create_flashcard(user=self.user, deck=deck)
        definition = self.factory.create_definition(flashcard)
        
        # Test relationships
        self.assertEqual(flashcard.deck, deck)
        self.assertEqual(flashcard.user, self.user)
        self.assertIn(flashcard, deck.flashcards.all())
        self.assertIn(definition, flashcard.definitions.all())


class PerformanceTestExample(PerformanceTestCase):
    """Example performance tests."""
    
    def test_query_performance(self):
        """Test database query performance."""
        # Create test data
        user, flashcards = self.factory.create_user_with_flashcards(count=100)
        
        # Test that querying flashcards doesn't exceed query limit
        def get_flashcards_with_definitions():
            return list(
                Flashcard.objects
                .filter(user=user)
                .select_related('deck', 'user')
                .prefetch_related('definitions')
            )
        
        # Test performance
        result = self.assert_performance(get_flashcards_with_definitions, max_time=0.5)
        self.assertEqual(len(result), 100)
        
        # Test query count
        query_count = self.benchmark_query(
            Flashcard.objects
            .filter(user=user)
            .select_related('deck', 'user')
            .prefetch_related('definitions'),
            max_queries=3  # Should be able to do this in 3 queries
        )
        self.assertLessEqual(query_count, 3)
    
    def test_spaced_repetition_algorithm_performance(self):
        """Test spaced repetition algorithm performance."""
        # Create many cards for performance testing
        cards = self.factory.create_spaced_repetition_cards(self.user, count=1000)
        
        def select_cards_for_study():
            from datetime import date
            return list(
                Flashcard.objects
                .filter(user=self.user)
                .filter(last_seen_date__lt=date.today())
                .order_by('difficulty_score', 'last_seen_date')[:20]
            )
        
        # Should be fast even with 1000 cards
        result = self.assert_performance(select_cards_for_study, max_time=0.1)
        self.assertLessEqual(len(result), 20)