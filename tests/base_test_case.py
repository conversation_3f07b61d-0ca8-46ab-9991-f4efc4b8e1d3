"""
Base test case classes with common testing utilities.
"""
from django.test import TestCase, TransactionTestCase
from django.contrib.auth import get_user_model
from django.core.management import call_command
from django.db import transaction
from .test_data_factory import TestDataFactory
from .api_test_client import APITestClient
from .mock_services import ExternalServiceMocker

User = get_user_model()


class BaseTestCase(TestCase):
    """Base test case with common setup and utilities."""
    
    @classmethod
    def setUpClass(cls):
        super().setUpClass()
        # Create test database tables
        call_command('migrate', verbosity=0, interactive=False)
    
    def setUp(self):
        """Set up test data and utilities."""
        super().setUp()
        self.factory = TestDataFactory()
        self.api_client = APITestClient()
        self.mock_services = ExternalServiceMocker()
        
        # Create a default test user
        self.user = self.factory.create_user()
        self.api_client.authenticate(self.user)
    
    def tearDown(self):
        """Clean up after tests."""
        super().tearDown()
        # Clean up any uploaded files
        import shutil
        import os
        from django.conf import settings
        
        test_media_root = getattr(settings, 'MEDIA_ROOT', None)
        if test_media_root and os.path.exists(test_media_root):
            try:
                shutil.rmtree(test_media_root)
            except OSError:
                pass
    
    def create_test_user(self, email="<EMAIL>", **kwargs):
        """Create a test user with default values."""
        return self.factory.create_user(email=email, **kwargs)
    
    def create_test_flashcard(self, user=None, **kwargs):
        """Create a test flashcard."""
        if not user:
            user = self.user
        return self.factory.create_flashcard(user=user, **kwargs)
    
    def create_test_deck(self, user=None, **kwargs):
        """Create a test deck."""
        if not user:
            user = self.user
        return self.factory.create_deck(user=user, **kwargs)
    
    def assert_model_fields(self, model_instance, expected_fields):
        """Assert that a model instance has the expected field values."""
        for field_name, expected_value in expected_fields.items():
            actual_value = getattr(model_instance, field_name)
            self.assertEqual(
                actual_value, 
                expected_value,
                f"Field '{field_name}' expected {expected_value}, got {actual_value}"
            )
    
    def assert_queryset_equal(self, qs1, qs2, transform=None, ordered=True):
        """Assert that two querysets are equal."""
        if transform:
            qs1 = [transform(item) for item in qs1]
            qs2 = [transform(item) for item in qs2]
        
        if ordered:
            self.assertEqual(list(qs1), list(qs2))
        else:
            self.assertCountEqual(list(qs1), list(qs2))


class APITestCase(BaseTestCase):
    """Base test case for API endpoint testing."""
    
    def setUp(self):
        super().setUp()
        # Additional API-specific setup
        self.api_base_url = '/api/v1/'
    
    def assert_api_response_format(self, response, expected_status=200):
        """Assert that API response follows the expected format."""
        return self.api_client.assert_response_format(response, expected_status)
    
    def assert_api_success(self, response, expected_data_keys=None):
        """Assert successful API response."""
        return self.api_client.assert_success_response(response, expected_data_keys)
    
    def assert_api_error(self, response, expected_status, expected_error_code=None):
        """Assert error API response."""
        return self.api_client.assert_error_response(response, expected_status, expected_error_code)
    
    def check_authentication_required(self, endpoints):
        """Helper method to test that endpoints require authentication."""
        self.api_client.logout()

        for endpoint in endpoints:
            response = self.api_client.get(endpoint)
            self.assertIn(response.status_code, [401, 403],
                         f"Endpoint {endpoint} should require authentication")

    def check_user_isolation(self, endpoint, create_data_func):
        """Helper method to test that users can only access their own data."""
        # Create data for first user
        user1_data = create_data_func(self.user)

        # Create second user and their data
        user2 = self.create_test_user(email="<EMAIL>")
        user2_data = create_data_func(user2)
        
        # Test that user1 can only see their data
        self.api_client.authenticate(self.user)
        response = self.api_client.get(endpoint)
        self.assert_api_success(response)
        
        # Verify user isolation (implementation depends on endpoint)
        # This is a template - specific tests should override this method


class ModelTestCase(BaseTestCase):
    """Base test case for model testing."""
    
    def assert_model_validation_error(self, model_class, field_name, invalid_value, **kwargs):
        """Assert that a model field validation raises an error."""
        from django.core.exceptions import ValidationError
        
        kwargs[field_name] = invalid_value
        instance = model_class(**kwargs)
        
        with self.assertRaises(ValidationError):
            instance.full_clean()
    
    def assert_model_str_representation(self, instance, expected_str):
        """Assert that model's __str__ method returns expected value."""
        self.assertEqual(str(instance), expected_str)
    
    def assert_model_property(self, instance, property_name, expected_value):
        """Assert that a model property returns the expected value."""
        actual_value = getattr(instance, property_name)
        self.assertEqual(actual_value, expected_value)


class IntegrationTestCase(TransactionTestCase):
    """Base test case for integration tests that require database transactions."""
    
    def setUp(self):
        super().setUp()
        self.factory = TestDataFactory()
        self.api_client = APITestClient()
        
        # Create test user
        self.user = self.factory.create_user()
        self.api_client.authenticate(self.user)
    
    def test_complete_workflow(self):
        """Template method for testing complete user workflows."""
        # Override in subclasses to test specific workflows
        pass
    
    def simulate_concurrent_access(self, func, num_threads=5):
        """Simulate concurrent access to test race conditions."""
        import threading
        import time
        
        results = []
        errors = []
        
        def worker():
            try:
                result = func()
                results.append(result)
            except Exception as e:
                errors.append(e)
        
        threads = []
        for _ in range(num_threads):
            thread = threading.Thread(target=worker)
            threads.append(thread)
            thread.start()
        
        for thread in threads:
            thread.join()
        
        return results, errors


class PerformanceTestCase(BaseTestCase):
    """Base test case for performance testing."""
    
    def setUp(self):
        super().setUp()
        self.performance_threshold = 1.0  # 1 second default threshold
    
    def assert_performance(self, func, max_time=None, *args, **kwargs):
        """Assert that a function executes within the time threshold."""
        import time
        
        if max_time is None:
            max_time = self.performance_threshold
        
        start_time = time.time()
        result = func(*args, **kwargs)
        end_time = time.time()
        
        execution_time = end_time - start_time
        self.assertLess(
            execution_time, 
            max_time,
            f"Function took {execution_time:.3f}s, expected less than {max_time}s"
        )
        
        return result
    
    def benchmark_query(self, queryset, max_queries=None):
        """Benchmark database queries."""
        from django.test.utils import override_settings
        from django.db import connection
        
        with override_settings(DEBUG=True):
            initial_queries = len(connection.queries)
            list(queryset)  # Force evaluation
            final_queries = len(connection.queries)
            
            query_count = final_queries - initial_queries
            
            if max_queries is not None:
                self.assertLessEqual(
                    query_count,
                    max_queries,
                    f"Query executed {query_count} queries, expected <= {max_queries}"
                )
            
            return query_count