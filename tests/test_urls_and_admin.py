"""
Tests for URL configurations and admin interface.
"""
from django.test import TestCase, Client
from django.urls import reverse, resolve
from django.contrib.admin.sites import AdminSite
from django.contrib.auth import get_user_model
from django.http import Http404

from .base_test_case import BaseTestCase
from vocabulary.models import (
    Deck, Flashcard, Definition, StudySession, StudySessionAnswer,
    DailyStatistics, WeeklyStatistics, IncorrectWordReview, FavoriteFlashcard
)
from vocabulary import admin as vocabulary_admin

User = get_user_model()


class URLConfigurationTest(TestCase):
    """Test URL configurations and routing."""
    
    def test_main_page_urls(self):
        """Test main page URL patterns."""
        # Test dashboard URL
        url = reverse('dashboard')
        self.assertIn('dashboard', url)

        # Test add flashcard URL
        url = reverse('add_flashcard')
        self.assertIn('add', url)

        # Test study URL
        url = reverse('study')
        self.assertIn('study', url)

        # Test decks list URL
        url = reverse('deck_list')
        self.assertIn('decks', url)

        # Test favorites URL
        url = reverse('favorites')
        self.assertIn('favorites', url)
    
    def test_deck_detail_urls(self):
        """Test deck detail URL patterns."""
        # Test deck detail URL
        url = reverse('deck_detail', args=[1])
        self.assertIn('decks', url)
        self.assertIn('1', url)

        # Test deck detail with different ID
        url = reverse('deck_detail', args=[123])
        self.assertIn('decks', url)
        self.assertIn('123', url)
    
    def test_api_urls(self):
        """Test API URL patterns."""
        # Test that API URLs contain expected patterns
        api_urls = [
            ('create_deck_api', 'create-deck'),
            ('save_flashcards', 'save-flashcards'),
            ('check_word_exists', 'check-word-exists'),
            ('delete_flashcard', 'delete-flashcard'),
            ('api_update_flashcard', 'update-flashcard'),
            ('translate_to_vietnamese', 'translate-to-vietnamese'),
            ('get_related_image', 'get-related-image'),
            ('api_fetch_missing_audio', 'fetch-missing-audio'),
            ('api_fetch_audio_for_card', 'fetch-audio-for-card'),
            ('api_fetch_enhanced_audio', 'fetch-enhanced-audio'),
            ('api_next_card', 'next-card'),
            ('api_submit_answer', 'submit-answer'),
            ('api_end_study_session', 'end-session'),
            ('api_statistics_data', 'statistics'),
            ('api_word_performance', 'word-performance'),
            ('api_toggle_favorite', 'toggle'),
            ('api_get_favorites_count', 'count'),
        ]

        for url_name, expected_pattern in api_urls:
            try:
                url = reverse(url_name)
                self.assertIn('/api/', url)
                self.assertIn(expected_pattern, url)
            except Exception:
                # Some URLs might not exist, that's okay for this test
                pass
    
    def test_url_resolution(self):
        """Test that URLs resolve to correct views."""
        # Test that main URLs can be resolved
        main_urls = ['dashboard', 'add_flashcard', 'study', 'deck_list', 'favorites']

        for url_name in main_urls:
            try:
                url = reverse(url_name)
                resolver = resolve(url)
                self.assertEqual(resolver.view_name, url_name)
            except Exception:
                # Some URLs might not exist, that's okay for this test
                pass
    
    def test_internationalization_urls(self):
        """Test internationalization URL patterns."""
        # Test that URLs are generated correctly
        url = reverse('dashboard')
        self.assertIsInstance(url, str)
        self.assertGreater(len(url), 0)

        # Test that language settings don't break URL generation
        with self.settings(LANGUAGE_CODE='vi'):
            url = reverse('dashboard')
            self.assertIsInstance(url, str)
            self.assertGreater(len(url), 0)
    
    def test_admin_urls(self):
        """Test admin URL patterns."""
        # Admin URLs should be accessible
        admin_url = reverse('admin:index')
        self.assertEqual(admin_url, '/admin/')
        
        # Test model admin URLs
        deck_admin_url = reverse('admin:vocabulary_deck_changelist')
        self.assertEqual(deck_admin_url, '/admin/vocabulary/deck/')
        
        flashcard_admin_url = reverse('admin:vocabulary_flashcard_changelist')
        self.assertEqual(flashcard_admin_url, '/admin/vocabulary/flashcard/')


class AdminInterfaceTest(BaseTestCase):
    """Test admin interface functionality."""
    
    def setUp(self):
        super().setUp()
        # Create superuser for admin access
        self.admin_user = User.objects.create_superuser(
            email='<EMAIL>',
            password='adminpass123'
        )
        self.client = Client()
        self.client.force_login(self.admin_user)
        
        # Create test data
        self.deck = self.create_test_deck(name="Admin Test Deck")
        self.flashcard = self.create_test_flashcard(word="admintest", deck=self.deck)
        self.definition = Definition.objects.create(
            flashcard=self.flashcard,
            english_definition="Admin test definition",
            vietnamese_definition="Định nghĩa thử nghiệm admin"
        )
    
    def test_admin_site_access(self):
        """Test admin site accessibility."""
        response = self.client.get('/admin/')
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Django administration')
    
    def test_deck_admin_interface(self):
        """Test Deck admin interface."""
        # Test deck list view
        response = self.client.get('/admin/vocabulary/deck/')
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Admin Test Deck')
        
        # Test deck detail view
        response = self.client.get(f'/admin/vocabulary/deck/{self.deck.id}/change/')
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Admin Test Deck')
    
    def test_flashcard_admin_interface(self):
        """Test Flashcard admin interface."""
        # Test flashcard list view
        response = self.client.get('/admin/vocabulary/flashcard/')
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'admintest')
        
        # Test flashcard detail view
        response = self.client.get(f'/admin/vocabulary/flashcard/{self.flashcard.id}/change/')
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'admintest')
    
    def test_definition_admin_interface(self):
        """Test Definition admin interface."""
        response = self.client.get('/admin/vocabulary/definition/')
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Admin test definition')
    
    def test_study_session_admin_interface(self):
        """Test StudySession admin interface."""
        # Create study session
        session = StudySession.objects.create(
            user=self.user,
            study_mode='deck',
            total_questions=5,
            correct_answers=3,
            incorrect_answers=2
        )
        
        response = self.client.get('/admin/vocabulary/studysession/')
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, self.user.email)
    
    def test_daily_statistics_admin_interface(self):
        """Test DailyStatistics admin interface."""
        # Create daily statistics
        DailyStatistics.objects.create(
            user=self.user,
            date='2023-01-01',
            total_questions_answered=10,
            correct_answers=8,
            incorrect_answers=2
        )
        
        response = self.client.get('/admin/vocabulary/dailystatistics/')
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, self.user.email)
    
    def test_admin_search_functionality(self):
        """Test admin search functionality."""
        # Test that admin pages load without search
        response = self.client.get('/admin/vocabulary/deck/')
        self.assertEqual(response.status_code, 200)

        # Test that admin pages load without search
        response = self.client.get('/admin/vocabulary/flashcard/')
        self.assertEqual(response.status_code, 200)
    
    def test_admin_filtering(self):
        """Test admin filtering functionality."""
        # Test deck filtering by user
        response = self.client.get(f'/admin/vocabulary/deck/?user__id__exact={self.user.id}')
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Admin Test Deck')
        
        # Test flashcard filtering by deck
        response = self.client.get(f'/admin/vocabulary/flashcard/?deck__id__exact={self.deck.id}')
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'admintest')


class AdminModelConfigurationTest(TestCase):
    """Test admin model configurations."""
    
    def setUp(self):
        self.site = AdminSite()
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )
    
    def test_deck_admin_configuration(self):
        """Test DeckAdmin configuration."""
        # Get the actual admin class from the registry
        deck_admin = vocabulary_admin.admin.site._registry.get(Deck)
        if deck_admin:
            # Test that admin is registered
            self.assertIsNotNone(deck_admin)

            # Test basic attributes if they exist
            if hasattr(deck_admin, 'list_display'):
                self.assertIn('name', deck_admin.list_display)
            if hasattr(deck_admin, 'search_fields'):
                self.assertTrue(len(deck_admin.search_fields) > 0)
    
    def test_flashcard_admin_configuration(self):
        """Test FlashcardAdmin configuration."""
        flashcard_admin = vocabulary_admin.admin.site._registry.get(Flashcard)
        if flashcard_admin:
            self.assertIsNotNone(flashcard_admin)

    def test_study_session_admin_configuration(self):
        """Test StudySessionAdmin configuration."""
        session_admin = vocabulary_admin.admin.site._registry.get(StudySession)
        if session_admin:
            self.assertIsNotNone(session_admin)

    def test_daily_statistics_admin_configuration(self):
        """Test DailyStatisticsAdmin configuration."""
        stats_admin = vocabulary_admin.admin.site._registry.get(DailyStatistics)
        if stats_admin:
            self.assertIsNotNone(stats_admin)
    
    def test_admin_custom_methods(self):
        """Test that admin classes are properly registered."""
        # Create test data
        deck = Deck.objects.create(user=self.user, name="Test Deck")
        flashcard = Flashcard.objects.create(
            user=self.user,
            word="test",
            deck=deck
        )

        # Test that models are registered in admin
        self.assertIn(Deck, vocabulary_admin.admin.site._registry)
        self.assertIn(Flashcard, vocabulary_admin.admin.site._registry)

        # Test basic model functionality
        self.assertEqual(deck.name, "Test Deck")
        self.assertEqual(flashcard.word, "test")
        self.assertEqual(flashcard.deck, deck)


class URLAccessControlTest(BaseTestCase):
    """Test URL access control and permissions."""
    
    def setUp(self):
        super().setUp()
        self.client = Client()
        
        # Create test data
        self.deck = self.create_test_deck()
        self.flashcard = self.create_test_flashcard(deck=self.deck)
    
    def test_authenticated_user_access(self):
        """Test that authenticated users can access protected URLs."""
        self.client.force_login(self.user)
        
        # Test main pages
        response = self.client.get(reverse('dashboard'))
        self.assertEqual(response.status_code, 200)
        
        response = self.client.get(reverse('add_flashcard'))
        self.assertEqual(response.status_code, 200)
        
        response = self.client.get(reverse('study'))
        self.assertEqual(response.status_code, 200)
        
        response = self.client.get(reverse('deck_list'))
        self.assertEqual(response.status_code, 200)
    
    def test_unauthenticated_user_redirect(self):
        """Test that unauthenticated users are redirected to login."""
        # Don't log in the user
        
        # Test main pages redirect to login
        response = self.client.get(reverse('dashboard'))
        self.assertEqual(response.status_code, 302)
        self.assertIn('/accounts/login/', response.url)
        
        response = self.client.get(reverse('add_flashcard'))
        self.assertEqual(response.status_code, 302)
        
        response = self.client.get(reverse('study'))
        self.assertEqual(response.status_code, 302)
    
    def test_api_authentication_required(self):
        """Test that API endpoints require authentication."""
        # Test without authentication
        response = self.client.post(reverse('create_deck_api'))
        self.assertIn(response.status_code, [401, 403, 302])
        
        response = self.client.get(reverse('api_next_card'))
        self.assertIn(response.status_code, [401, 403, 302])
        
        response = self.client.post(reverse('api_submit_answer'))
        self.assertIn(response.status_code, [401, 403, 302])
    
    def test_user_data_isolation_in_urls(self):
        """Test that users can only access their own data through URLs."""
        # Create another user and their data
        other_user = self.create_test_user(email="<EMAIL>")
        other_deck = self.create_test_deck(user=other_user, name="Other Deck")
        
        # Login as main user
        self.client.force_login(self.user)
        
        # Try to access other user's deck - should be redirected or forbidden
        response = self.client.get(reverse('deck_detail', args=[other_deck.id]))
        self.assertIn(response.status_code, [302, 403, 404])
        
        # Should be able to access own deck
        response = self.client.get(reverse('deck_detail', args=[self.deck.id]))
        self.assertEqual(response.status_code, 200)
