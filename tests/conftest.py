"""
Test configuration and fixtures for pytest (if used) and Django tests.
"""
import os
import django
from django.conf import settings
from django.test.utils import get_runner

# Configure Django settings for tests
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'tests.test_settings')

def setup_test_environment():
    """Set up the test environment."""
    django.setup()
    
    # Create test database
    from django.core.management import call_command
    call_command('migrate', verbosity=0, interactive=False)

def teardown_test_environment():
    """Clean up the test environment."""
    # Clean up test files
    import shutil
    import tempfile
    
    test_dirs = ['/tmp/test_media', '/tmp/test_static']
    for test_dir in test_dirs:
        if os.path.exists(test_dir):
            try:
                shutil.rmtree(test_dir)
            except OSError:
                pass

# Test runner configuration
class CustomTestRunner:
    """Custom test runner with additional setup."""
    
    def __init__(self):
        self.test_runner = None
    
    def setup_test_environment(self):
        """Set up test environment."""
        setup_test_environment()
        
        # Set up test runner
        TestRunner = get_runner(settings)
        self.test_runner = TestRunner(verbosity=1, interactive=False)
        self.test_runner.setup_test_environment()
    
    def run_tests(self, test_labels):
        """Run the specified tests."""
        if not self.test_runner:
            self.setup_test_environment()
        
        old_config = self.test_runner.setup_databases()
        result = self.test_runner.run_tests(test_labels)
        self.test_runner.teardown_databases(old_config)
        
        return result
    
    def teardown_test_environment(self):
        """Tear down test environment."""
        if self.test_runner:
            self.test_runner.teardown_test_environment()
        teardown_test_environment()

# Coverage configuration
COVERAGE_CONFIG = {
    'source': ['vocabulary', 'accounts', 'learn_english_project'],
    'omit': [
        '*/migrations/*',
        '*/venv/*',
        '*/env/*',
        '*/tests/*',
        'manage.py',
        '*/settings/*',
        '*/wsgi.py',
        '*/asgi.py',
    ],
    'exclude_lines': [
        'pragma: no cover',
        'def __repr__',
        'if self.debug:',
        'if settings.DEBUG',
        'raise AssertionError',
        'raise NotImplementedError',
        'if 0:',
        'if __name__ == .__main__.:',
        'class .*\bProtocol\):',
        '@(abc\.)?abstractmethod',
    ]
}

# Test data configuration
TEST_DATA_CONFIG = {
    'default_user_count': 5,
    'default_flashcard_count': 20,
    'default_deck_count': 3,
    'default_session_count': 10,
}

# Performance test thresholds
PERFORMANCE_THRESHOLDS = {
    'api_response_time': 0.5,  # 500ms
    'database_query_time': 0.1,  # 100ms
    'page_load_time': 2.0,  # 2 seconds
    'max_queries_per_request': 10,
}

# Mock API configurations
MOCK_API_CONFIG = {
    'datamuse_api_delay': 0.1,  # Simulate API delay
    'languagetool_api_delay': 0.2,
    'unsplash_api_delay': 0.15,
    'google_translate_delay': 0.1,
}