"""
Comprehensive Test Suite for the Learn English Vocabulary application.

This directory contains comprehensive test coverage for all application components:

## Core Component Tests:
- test_audio_service.py: Audio fetching and Cambridge Dictionary integration
- test_statistics_utils.py: Statistics calculation and session management
- test_word_details_service.py: Word lookup and details service
- test_context_processors.py: Template context and i18n processing
- test_management_commands.py: Django management commands
- test_urls_and_admin.py: URL configurations and admin interface

## Integration Tests:
- test_integration_workflows.py: Complete user workflows and end-to-end testing

## Test Infrastructure:
- base_test_case.py: Base test classes with common utilities
- test_data_factory.py: Test data generation factory
- api_test_client.py: API testing client with response validation
- mock_services.py: External service mocking utilities
- conftest.py: Test configuration and setup

## Legacy Test Files:
- Various feature-specific test files for UI/UX improvements
- Enhanced audio features testing
- Favorites system testing
- Deck navigation testing
- Audio statistics testing

## Usage:
Run all tests: python run_tests.py
Run specific test suite: python run_tests.py --suite models
Run with coverage: python run_tests.py (coverage enabled by default)
"""
