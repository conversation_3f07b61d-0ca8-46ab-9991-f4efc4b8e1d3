"""
Mock services for testing external API integrations.
"""
from unittest.mock import Mock, patch
import json


class MockDatamuseAPI:
    """Mock Datamuse API responses."""
    
    @staticmethod
    def mock_word_suggestions(word="test"):
        """Mock word suggestions response."""
        return [
            {"word": f"{word}_suggestion1", "score": 100},
            {"word": f"{word}_suggestion2", "score": 90},
            {"word": f"{word}_suggestion3", "score": 80},
        ]
    
    @staticmethod
    def mock_word_definitions(word="test"):
        """Mock word definitions response."""
        return [
            {
                "word": word,
                "defs": [
                    "n:a procedure intended to establish the quality, performance, or reliability of something",
                    "v:take measures to check the quality, performance, or reliability of something"
                ]
            }
        ]
    
    @staticmethod
    def mock_synonyms(word="test"):
        """Mock synonyms response."""
        return [
            {"word": "examination", "score": 95},
            {"word": "trial", "score": 90},
            {"word": "assessment", "score": 85},
        ]


class MockLanguageToolAPI:
    """Mock LanguageTool API responses."""
    
    @staticmethod
    def mock_spell_check_correct(text="This is correct text"):
        """Mock spell check response for correct text."""
        return {
            "software": {"name": "LanguageTool", "version": "5.8"},
            "warnings": {"incompleteResults": False},
            "language": {"name": "English", "code": "en-US"},
            "matches": []
        }
    
    @staticmethod
    def mock_spell_check_errors(text="This is incorect text"):
        """Mock spell check response with errors."""
        return {
            "software": {"name": "LanguageTool", "version": "5.8"},
            "warnings": {"incompleteResults": False},
            "language": {"name": "English", "code": "en-US"},
            "matches": [
                {
                    "message": "Possible spelling mistake found.",
                    "shortMessage": "Spelling mistake",
                    "replacements": [{"value": "incorrect"}],
                    "offset": 8,
                    "length": 8,
                    "context": {"text": "This is incorect text", "offset": 8, "length": 8},
                    "sentence": "This is incorect text",
                    "type": {"typeName": "misspelling"},
                    "rule": {
                        "id": "MORFOLOGIK_RULE_EN_US",
                        "description": "Possible spelling mistake",
                        "issueType": "misspelling",
                        "category": {"id": "TYPOS", "name": "Possible Typo"}
                    }
                }
            ]
        }


class MockUnsplashAPI:
    """Mock Unsplash API responses."""
    
    @staticmethod
    def mock_search_photos(query="test"):
        """Mock photo search response."""
        return {
            "total": 100,
            "total_pages": 10,
            "results": [
                {
                    "id": "test_photo_1",
                    "urls": {
                        "small": f"https://images.unsplash.com/photo-1?w=400&q=80&{query}",
                        "regular": f"https://images.unsplash.com/photo-1?w=1080&q=80&{query}",
                    },
                    "alt_description": f"A photo related to {query}",
                    "user": {"name": "Test Photographer"},
                },
                {
                    "id": "test_photo_2",
                    "urls": {
                        "small": f"https://images.unsplash.com/photo-2?w=400&q=80&{query}",
                        "regular": f"https://images.unsplash.com/photo-2?w=1080&q=80&{query}",
                    },
                    "alt_description": f"Another photo related to {query}",
                    "user": {"name": "Another Photographer"},
                }
            ]
        }


class MockGoogleTranslateAPI:
    """Mock Google Translate API responses."""
    
    @staticmethod
    def mock_translate(text="hello", target_lang="vi"):
        """Mock translation response."""
        translations = {
            "hello": {"vi": "xin chào", "es": "hola", "fr": "bonjour"},
            "test": {"vi": "thử nghiệm", "es": "prueba", "fr": "test"},
            "word": {"vi": "từ", "es": "palabra", "fr": "mot"},
        }
        
        translated_text = translations.get(text.lower(), {}).get(target_lang, f"translated_{text}")
        
        return {
            "translatedText": translated_text,
            "detectedSourceLanguage": "en"
        }


class ExternalServiceMocker:
    """Context manager for mocking external services during tests."""
    
    def __init__(self):
        self.patches = []
    
    def __enter__(self):
        # Mock requests.get for external API calls
        self.requests_patch = patch('requests.get')
        self.mock_requests = self.requests_patch.start()
        self.patches.append(self.requests_patch)
        
        # Mock requests.post for external API calls
        self.requests_post_patch = patch('requests.post')
        self.mock_requests_post = self.requests_post_patch.start()
        self.patches.append(self.requests_post_patch)
        
        # Set up default responses
        self._setup_default_responses()
        
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        for patch_obj in self.patches:
            patch_obj.stop()
    
    def _setup_default_responses(self):
        """Set up default mock responses for common API calls."""
        def mock_response(url, *args, **kwargs):
            mock_resp = Mock()
            mock_resp.status_code = 200
            
            # Datamuse API
            if 'api.datamuse.com' in url:
                if 'ml=' in url:  # Word suggestions
                    mock_resp.json.return_value = MockDatamuseAPI.mock_word_suggestions()
                elif 'sp=' in url:  # Definitions
                    mock_resp.json.return_value = MockDatamuseAPI.mock_word_definitions()
                elif 'rel_syn=' in url:  # Synonyms
                    mock_resp.json.return_value = MockDatamuseAPI.mock_synonyms()
            
            # LanguageTool API
            elif 'languagetool' in url:
                mock_resp.json.return_value = MockLanguageToolAPI.mock_spell_check_correct()
            
            # Unsplash API
            elif 'unsplash.com' in url:
                mock_resp.json.return_value = MockUnsplashAPI.mock_search_photos()
            
            # Google Translate API
            elif 'translate.googleapis.com' in url:
                mock_resp.json.return_value = MockGoogleTranslateAPI.mock_translate()
            
            else:
                # Default response
                mock_resp.json.return_value = {"status": "success", "data": {}}
            
            return mock_resp
        
        def mock_post_response(url, *args, **kwargs):
            mock_resp = Mock()
            mock_resp.status_code = 200
            
            # LanguageTool API POST requests
            if 'languagetool' in url:
                data = kwargs.get('data', {})
                text = data.get('text', '') if isinstance(data, dict) else ''
                if 'incorect' in text or 'wrng' in text:
                    mock_resp.json.return_value = MockLanguageToolAPI.mock_spell_check_errors(text)
                else:
                    mock_resp.json.return_value = MockLanguageToolAPI.mock_spell_check_correct(text)
            else:
                mock_resp.json.return_value = {"status": "success", "data": {}}
            
            return mock_resp
        
        self.mock_requests.side_effect = mock_response
        self.mock_requests_post.side_effect = mock_post_response
    
    def set_api_response(self, url_pattern, response_data, status_code=200):
        """Set a specific response for a URL pattern."""
        def custom_response(url, *args, **kwargs):
            if url_pattern in url:
                mock_resp = Mock()
                mock_resp.status_code = status_code
                mock_resp.json.return_value = response_data
                return mock_resp
            return self._setup_default_responses()(url, *args, **kwargs)
        
        self.mock_requests.side_effect = custom_response
    
    def set_api_error(self, url_pattern, status_code=500, error_message="API Error"):
        """Set an error response for a URL pattern."""
        def error_response(url, *args, **kwargs):
            if url_pattern in url:
                mock_resp = Mock()
                mock_resp.status_code = status_code
                mock_resp.json.return_value = {"error": error_message}
                mock_resp.raise_for_status.side_effect = Exception(error_message)
                return mock_resp
            return self._setup_default_responses()(url, *args, **kwargs)
        
        self.mock_requests.side_effect = error_response


# Convenience decorators for common mocking scenarios
def mock_external_apis(test_func):
    """Decorator to mock all external APIs for a test."""
    def wrapper(*args, **kwargs):
        with ExternalServiceMocker():
            return test_func(*args, **kwargs)
    return wrapper


def mock_datamuse_api(test_func):
    """Decorator to mock only Datamuse API."""
    def wrapper(*args, **kwargs):
        with patch('requests.get') as mock_get:
            mock_get.return_value.status_code = 200
            mock_get.return_value.json.return_value = MockDatamuseAPI.mock_word_suggestions()
            return test_func(*args, **kwargs)
    return wrapper


def mock_languagetool_api(test_func):
    """Decorator to mock only LanguageTool API."""
    def wrapper(*args, **kwargs):
        with patch('requests.post') as mock_post:
            mock_post.return_value.status_code = 200
            mock_post.return_value.json.return_value = MockLanguageToolAPI.mock_spell_check_correct()
            return test_func(*args, **kwargs)
    return wrapper