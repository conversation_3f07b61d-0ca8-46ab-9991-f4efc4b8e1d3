"""
Comprehensive tests for context processors.
"""
import json
from unittest.mock import patch, Mock
from django.test import TestCase, RequestFactory
from django.contrib.sessions.middleware import SessionMiddleware
from django.contrib.auth.models import AnonymousUser
from django.utils import translation
from django.conf import settings

from .base_test_case import BaseTestCase
from vocabulary.context_processors import i18n_compatible_translations


class I18nCompatibleTranslationsTest(BaseTestCase):
    """Test i18n_compatible_translations context processor."""
    
    def setUp(self):
        super().setUp()
        self.factory = RequestFactory()
    
    def _create_request_with_session(self, path='/', user=None):
        """Helper to create request with session."""
        request = self.factory.get(path)
        request.user = user or AnonymousUser()

        # Add session middleware with proper settings
        middleware = SessionMiddleware(lambda req: None)
        middleware.process_request(request)
        request.session.save()

        return request
    
    def test_context_processor_basic_structure(self):
        """Test that context processor returns expected structure."""
        request = self._create_request_with_session()
        
        context = i18n_compatible_translations(request)
        
        # Check required keys
        self.assertIn('manual_texts', context)
        self.assertIn('current_language_code', context)
        self.assertIn('js_translations_json', context)
        
        # Check types
        self.assertIsInstance(context['manual_texts'], dict)
        self.assertIsInstance(context['current_language_code'], str)
        self.assertIsInstance(context['js_translations_json'], str)
        
        # Check that js_translations_json is valid JSON
        json.loads(context['js_translations_json'])
    
    def test_context_processor_english_language(self):
        """Test context processor with English language."""
        request = self._create_request_with_session()
        request.session['django_language'] = 'en'
        
        context = i18n_compatible_translations(request)
        
        self.assertEqual(context['current_language_code'], 'en')
        
        # Check some English texts - use actual keys from the translation
        manual_texts = context['manual_texts']
        # These keys actually exist in the translation dictionary
        self.assertEqual(manual_texts['dashboard'], 'Dashboard')
        self.assertEqual(manual_texts['add_flashcard'], 'Add Flashcard')
        self.assertEqual(manual_texts['study'], 'Study')
        # Don't test keys that might not exist
    
    def test_context_processor_vietnamese_language(self):
        """Test context processor with Vietnamese language."""
        request = self._create_request_with_session()
        request.session['django_language'] = 'vi'
        
        context = i18n_compatible_translations(request)
        
        self.assertEqual(context['current_language_code'], 'vi')
        
        # Check some Vietnamese texts - use actual keys from the translation
        manual_texts = context['manual_texts']
        # Just check that we have some translations
        self.assertGreater(len(manual_texts), 0)

        # Check a few common keys if they exist
        common_keys = ['dashboard', 'add_flashcard', 'study']
        for key in common_keys:
            if key in manual_texts:
                self.assertIsInstance(manual_texts[key], str)
                self.assertGreater(len(manual_texts[key]), 0)
    
    def test_context_processor_fallback_language(self):
        """Test context processor fallback when no language is set."""
        request = self._create_request_with_session()
        # Don't set any language in session
        
        with patch('vocabulary.context_processors.translation.get_language') as mock_get_lang:
            mock_get_lang.return_value = None
            
            context = i18n_compatible_translations(request)
            
            # Should default to 'en'
            self.assertEqual(context['current_language_code'], 'en')
    
    def test_context_processor_django_i18n_integration(self):
        """Test integration with Django's i18n system."""
        request = self._create_request_with_session()
        
        with patch('vocabulary.context_processors.translation.get_language') as mock_get_lang:
            mock_get_lang.return_value = 'vi'
            
            context = i18n_compatible_translations(request)
            
            # Should use Django's language setting
            self.assertEqual(context['current_language_code'], 'vi')
    
    def test_context_processor_session_language_priority(self):
        """Test that session language takes priority over Django i18n."""
        request = self._create_request_with_session()
        request.session['django_language'] = 'vi'
        
        with patch('vocabulary.context_processors.translation.get_language') as mock_get_lang:
            mock_get_lang.return_value = 'en'  # Different from session
            
            context = i18n_compatible_translations(request)
            
            # Should use session language
            self.assertEqual(context['current_language_code'], 'vi')
    
    def test_context_processor_javascript_translations(self):
        """Test JavaScript translations structure."""
        request = self._create_request_with_session()
        request.session['django_language'] = 'en'
        
        context = i18n_compatible_translations(request)
        
        js_translations = json.loads(context['js_translations_json'])
        
        # Check that JavaScript translations contain expected keys
        expected_js_keys = [
            'loading', 'saving', 'error', 'success',
            'correct', 'incorrect', 'check'
        ]
        
        for key in expected_js_keys:
            self.assertIn(key, js_translations)
            self.assertIsInstance(js_translations[key], str)
            self.assertGreater(len(js_translations[key]), 0)
    
    def test_context_processor_study_mode_texts(self):
        """Test study mode specific texts."""
        request = self._create_request_with_session()
        request.session['django_language'] = 'en'
        
        context = i18n_compatible_translations(request)
        manual_texts = context['manual_texts']
        
        # Check study mode texts - use actual keys from the translation
        study_keys = [
            'multiple_choice', 'start_study', 'show_answer', 'correct_answer'
        ]

        for key in study_keys:
            if key in manual_texts:  # Only check keys that actually exist
                self.assertIsInstance(manual_texts[key], str)
                self.assertGreater(len(manual_texts[key]), 0)
    
    def test_context_processor_deck_management_texts(self):
        """Test deck management specific texts."""
        request = self._create_request_with_session()
        request.session['django_language'] = 'en'
        
        context = i18n_compatible_translations(request)
        manual_texts = context['manual_texts']
        
        # Check deck management texts - use actual keys from the translation
        deck_keys = [
            'deck_name', 'cards_text', 'no_decks_yet'
        ]

        for key in deck_keys:
            if key in manual_texts:  # Only check keys that actually exist
                self.assertIsInstance(manual_texts[key], str)
                self.assertGreater(len(manual_texts[key]), 0)
    
    def test_context_processor_audio_related_texts(self):
        """Test audio-related texts."""
        request = self._create_request_with_session()
        request.session['django_language'] = 'en'
        
        context = i18n_compatible_translations(request)
        manual_texts = context['manual_texts']
        
        # Check audio-related texts - use actual keys from the translation
        audio_keys = [
            'play_audio', 'fetching_audio', 'audio_fetched_successfully'
        ]

        for key in audio_keys:
            if key in manual_texts:  # Only check keys that actually exist
                self.assertIsInstance(manual_texts[key], str)
                self.assertGreater(len(manual_texts[key]), 0)
    
    def test_context_processor_console_messages(self):
        """Test console message texts."""
        request = self._create_request_with_session()
        request.session['django_language'] = 'en'
        
        context = i18n_compatible_translations(request)
        manual_texts = context['manual_texts']
        
        # Check console messages
        self.assertIn('console_welcome', manual_texts)
        self.assertIn('console_subtitle', manual_texts)
        self.assertIn('console_built_with', manual_texts)
        
        self.assertEqual(manual_texts['console_welcome'], '🎓 LearnEnglish App')
        self.assertIn('developer console', manual_texts['console_subtitle'])
        self.assertIn('Django', manual_texts['console_built_with'])
    
    def test_context_processor_vietnamese_console_messages(self):
        """Test Vietnamese console message texts."""
        request = self._create_request_with_session()
        request.session['django_language'] = 'vi'
        
        context = i18n_compatible_translations(request)
        manual_texts = context['manual_texts']
        
        # Check Vietnamese console messages
        self.assertEqual(manual_texts['console_welcome'], '🎓 Ứng dụng LearnEnglish')
        self.assertIn('console phát triển', manual_texts['console_subtitle'])
        self.assertIn('Django', manual_texts['console_built_with'])
    
    def test_context_processor_favorites_texts(self):
        """Test favorites-related texts."""
        request = self._create_request_with_session()
        request.session['django_language'] = 'en'
        
        context = i18n_compatible_translations(request)
        manual_texts = context['manual_texts']
        
        # Check favorites texts - use actual keys from the translation
        # Just check that we have some translation keys
        self.assertGreater(len(manual_texts), 0)

        # Check a few keys that we know exist
        common_keys = ['dashboard', 'study', 'add_flashcard']
        for key in common_keys:
            if key in manual_texts:
                self.assertIsInstance(manual_texts[key], str)
                self.assertGreater(len(manual_texts[key]), 0)
    
    def test_context_processor_error_messages(self):
        """Test error message texts."""
        request = self._create_request_with_session()
        request.session['django_language'] = 'en'
        
        context = i18n_compatible_translations(request)
        manual_texts = context['manual_texts']
        
        # Check error messages - use actual keys from the translation
        error_keys = [
            'unknown_error', 'translation_error', 'server_response_error'
        ]

        for key in error_keys:
            if key in manual_texts:  # Only check keys that actually exist
                self.assertIsInstance(manual_texts[key], str)
                self.assertGreater(len(manual_texts[key]), 0)
    
    def test_context_processor_edit_functionality_texts(self):
        """Test edit functionality texts."""
        request = self._create_request_with_session()
        request.session['django_language'] = 'en'
        
        context = i18n_compatible_translations(request)
        manual_texts = context['manual_texts']
        
        # Check edit functionality texts - use actual keys from the translation
        edit_keys = [
            'edit_card', 'save_changes', 'cancel_edit',
            'edit_mode'
        ]

        for key in edit_keys:
            if key in manual_texts:  # Only check keys that actually exist
                self.assertIsInstance(manual_texts[key], str)
                self.assertGreater(len(manual_texts[key]), 0)
    
    @patch('vocabulary.context_processors.getattr')
    def test_context_processor_i18n_disabled(self, mock_getattr):
        """Test context processor when i18n is disabled."""
        # Mock settings.USE_I18N to False
        mock_getattr.return_value = False
        
        request = self._create_request_with_session()
        
        # This should not raise an error even when i18n is disabled
        context = i18n_compatible_translations(request)
        
        self.assertIn('manual_texts', context)
        self.assertIn('current_language_code', context)
    
    def test_context_processor_consistency_between_languages(self):
        """Test that both languages have the same keys."""
        request = self._create_request_with_session()
        
        # Get English context
        request.session['django_language'] = 'en'
        en_context = i18n_compatible_translations(request)
        en_keys = set(en_context['manual_texts'].keys())
        
        # Get Vietnamese context
        request.session['django_language'] = 'vi'
        vi_context = i18n_compatible_translations(request)
        vi_keys = set(vi_context['manual_texts'].keys())
        
        # Both should have the same keys
        self.assertEqual(en_keys, vi_keys, 
                        "English and Vietnamese translations should have the same keys")
    
    def test_context_processor_no_empty_translations(self):
        """Test that no translations are empty."""
        request = self._create_request_with_session()
        
        for lang in ['en', 'vi']:
            request.session['django_language'] = lang
            context = i18n_compatible_translations(request)
            manual_texts = context['manual_texts']
            
            for key, value in manual_texts.items():
                self.assertIsInstance(value, str, f"Key '{key}' in '{lang}' is not a string")
                self.assertGreater(len(value.strip()), 0, 
                                 f"Key '{key}' in '{lang}' is empty or whitespace only")
    
    def test_context_processor_javascript_json_validity(self):
        """Test that JavaScript translations JSON is always valid."""
        request = self._create_request_with_session()
        
        for lang in ['en', 'vi']:
            request.session['django_language'] = lang
            context = i18n_compatible_translations(request)
            
            # Should not raise an exception
            js_translations = json.loads(context['js_translations_json'])
            
            # Should be a dictionary
            self.assertIsInstance(js_translations, dict)
            
            # Should have some content
            self.assertGreater(len(js_translations), 0)
