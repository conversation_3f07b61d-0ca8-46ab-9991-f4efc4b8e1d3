"""
Test utilities for vocabulary app to improve test output and suppress expected warnings.
"""
import logging
import warnings
from contextlib import contextmanager
from django.test.utils import override_settings
from django.test import TestCase as DjangoTestCase


class SuppressLoggingMixin:
    """Mixin to suppress logging during tests."""
    
    def setUp(self):
        super().setUp()
        # Suppress API service warnings during tests
        logging.getLogger('vocabulary.api_services').setLevel(logging.ERROR)
        
        # Suppress Django timezone warnings during tests
        warnings.filterwarnings('ignore', 
                              message='DateTimeField .* received a naive datetime',
                              category=RuntimeWarning)
    
    def tearDown(self):
        super().tearDown()
        # Reset logging level
        logging.getLogger('vocabulary.api_services').setLevel(logging.WARNING)
        
        # Reset warnings
        warnings.resetwarnings()


@contextmanager
def suppress_api_logs():
    """Context manager to suppress API service logs during specific test operations."""
    logger = logging.getLogger('vocabulary.api_services')
    original_level = logger.level
    logger.setLevel(logging.ERROR)
    try:
        yield
    finally:
        logger.setLevel(original_level)


@contextmanager
def suppress_timezone_warnings():
    """Context manager to suppress Django timezone warnings during specific test operations."""
    with warnings.catch_warnings():
        warnings.filterwarnings('ignore', 
                              message='DateTimeField .* received a naive datetime',
                              category=RuntimeWarning)
        yield


@contextmanager
def suppress_all_test_warnings():
    """Context manager to suppress all expected test warnings."""
    with suppress_api_logs(), suppress_timezone_warnings():
        yield


# Test settings for cleaner output
TEST_LOGGING_CONFIG = {
    'version': 1,
    'disable_existing_loggers': False,
    'handlers': {
        'null': {
            'class': 'logging.NullHandler',
        },
    },
    'loggers': {
        'vocabulary.api_services': {
            'handlers': ['null'],
            'level': 'ERROR',
            'propagate': False,
        },
    },
}


def configure_test_logging():
    """Configure logging for cleaner test output."""
    import logging.config
    logging.config.dictConfig(TEST_LOGGING_CONFIG)


def silence_expected_warnings():
    """Silence expected warnings that appear during testing."""
    # Suppress Django timezone warnings - multiple patterns to catch all variations
    warnings.filterwarnings('ignore',
                          message='DateTimeField .* received a naive datetime',
                          category=RuntimeWarning)

    warnings.filterwarnings('ignore',
                          message='.*received a naive datetime.*',
                          category=RuntimeWarning)

    warnings.filterwarnings('ignore',
                          message='.*naive datetime.*',
                          category=RuntimeWarning)

    # Suppress any Django field warnings
    warnings.filterwarnings('ignore',
                          message='.*DateTimeField.*',
                          category=RuntimeWarning)

    # Suppress timezone-related warnings
    warnings.filterwarnings('ignore',
                          message='.*time zone support is active.*',
                          category=RuntimeWarning)


class CleanTestOutputMixin:
    """Mixin that provides clean test output by suppressing expected warnings."""

    @classmethod
    def setUpClass(cls):
        super().setUpClass()
        configure_test_logging()
        silence_expected_warnings()

    def setUp(self):
        super().setUp()
        # Apply warning filters for each test
        silence_expected_warnings()

    @classmethod
    def tearDownClass(cls):
        super().tearDownClass()
        # Reset warnings
        warnings.resetwarnings()

        # Reset logging
        logging.getLogger('vocabulary.api_services').setLevel(logging.WARNING)


class TimezoneAwareTestCase(CleanTestOutputMixin, DjangoTestCase):
    """TestCase that automatically handles timezone-aware datetime objects."""

    def create_flashcard(self, user, word, **kwargs):
        """Helper method to create flashcard with timezone-aware datetime."""
        from django.utils import timezone
        flashcard = self.model_class.objects.create(user=user, word=word, **kwargs)
        flashcard.created_at = timezone.now()
        flashcard.save()
        return flashcard

    def setUp(self):
        super().setUp()
        # Import here to avoid circular imports
        from .models import Flashcard
        self.model_class = Flashcard
