"""
Test-specific settings to provide cleaner test output.
"""
import warnings
import logging
import logging.config

# Suppress ALL Django timezone warnings during testing
warnings.filterwarnings('ignore',
                      message='DateTimeField .* received a naive datetime',
                      category=RuntimeWarning)

warnings.filterwarnings('ignore',
                      message='.*received a naive datetime.*',
                      category=RuntimeWarning)

warnings.filterwarnings('ignore',
                      message='.*naive datetime.*',
                      category=RuntimeWarning)

warnings.filterwarnings('ignore',
                      message='.*DateTimeField.*',
                      category=RuntimeWarning)

warnings.filterwarnings('ignore',
                      message='.*time zone support is active.*',
                      category=RuntimeWarning)

# Suppress any Django model field warnings
warnings.filterwarnings('ignore',
                      category=RuntimeWarning,
                      module='django.db.models.fields')

# Configure logging for cleaner test output
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'handlers': {
        'null': {
            'class': 'logging.NullHandler',
        },
        'console': {
            'class': 'logging.StreamHandler',
            'level': 'ERROR',  # Only show errors during tests
        },
    },
    'loggers': {
        'vocabulary.api_services': {
            'handlers': ['null'],
            'level': 'ERROR',
            'propagate': False,
        },
        'django': {
            'handlers': ['console'],
            'level': 'ERROR',
            'propagate': False,
        },
    },
    'root': {
        'handlers': ['console'],
        'level': 'ERROR',
    },
}

# Apply logging configuration
logging.config.dictConfig(LOGGING)
