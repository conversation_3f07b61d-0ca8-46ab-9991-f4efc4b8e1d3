"""
Comprehensive test cases for vocabulary API services to improve code coverage.
"""
import json
from unittest.mock import patch, Mock
import requests

from django.test import TestCase

from .api_services import get_word_suggestions_from_datamuse, check_word_spelling_with_languagetool
from .test_utils import CleanTestOutputMixin, suppress_api_logs


class DatamuseAPITest(CleanTestOutputMixin, TestCase):
    """Test Datamuse API integration."""
    
    @patch('vocabulary.api_services.requests.get')
    def test_get_word_suggestions_success(self, mock_get):
        """Test successful word suggestions retrieval."""
        # Mock successful API response
        mock_response = Mock()
        mock_response.json.return_value = [
            {'word': 'serendipity', 'score': 100},
            {'word': 'serendipitous', 'score': 95},
            {'word': 'serene', 'score': 80}
        ]
        mock_response.raise_for_status.return_value = None
        mock_get.return_value = mock_response
        
        suggestions = get_word_suggestions_from_datamuse('seren')
        
        self.assertEqual(len(suggestions), 3)
        self.assertIn('serendipity', suggestions)
        self.assertIn('serendipitous', suggestions)
        self.assertIn('serene', suggestions)
        
        # Verify API was called correctly
        mock_get.assert_called_once_with('https://api.datamuse.com/sug?s=seren')
    
    @patch('vocabulary.api_services.requests.get')
    def test_get_word_suggestions_empty_query(self, mock_get):
        """Test word suggestions with empty query."""
        suggestions = get_word_suggestions_from_datamuse('')
        
        self.assertEqual(suggestions, [])
        mock_get.assert_not_called()
    
    @patch('vocabulary.api_services.requests.get')
    def test_get_word_suggestions_none_query(self, mock_get):
        """Test word suggestions with None query."""
        suggestions = get_word_suggestions_from_datamuse(None)
        
        self.assertEqual(suggestions, [])
        mock_get.assert_not_called()
    
    @patch('vocabulary.api_services.requests.get')
    def test_get_word_suggestions_request_exception(self, mock_get):
        """Test word suggestions with request exception."""
        mock_get.side_effect = requests.exceptions.RequestException("Network error")

        with suppress_api_logs():
            suggestions = get_word_suggestions_from_datamuse('test')

        self.assertEqual(suggestions, [])
        mock_get.assert_called_once()
    
    @patch('vocabulary.api_services.requests.get')
    def test_get_word_suggestions_http_error(self, mock_get):
        """Test word suggestions with HTTP error."""
        mock_response = Mock()
        mock_response.raise_for_status.side_effect = requests.exceptions.HTTPError("404 Not Found")
        mock_get.return_value = mock_response

        with suppress_api_logs():
            suggestions = get_word_suggestions_from_datamuse('test')

        self.assertEqual(suggestions, [])
        mock_get.assert_called_once()
    
    @patch('vocabulary.api_services.requests.get')
    def test_get_word_suggestions_json_decode_error(self, mock_get):
        """Test word suggestions with JSON decode error."""
        mock_response = Mock()
        mock_response.raise_for_status.return_value = None
        mock_response.json.side_effect = ValueError("Invalid JSON")
        mock_get.return_value = mock_response

        with suppress_api_logs():
            suggestions = get_word_suggestions_from_datamuse('test')

        self.assertEqual(suggestions, [])
        mock_get.assert_called_once()
    
    @patch('vocabulary.api_services.requests.get')
    def test_get_word_suggestions_empty_response(self, mock_get):
        """Test word suggestions with empty API response."""
        mock_response = Mock()
        mock_response.json.return_value = []
        mock_response.raise_for_status.return_value = None
        mock_get.return_value = mock_response
        
        suggestions = get_word_suggestions_from_datamuse('nonexistentword')
        
        self.assertEqual(suggestions, [])
        mock_get.assert_called_once()


class LanguageToolAPITest(CleanTestOutputMixin, TestCase):
    """Test LanguageTool API integration."""
    
    @patch('vocabulary.api_services.requests.post')
    def test_check_word_spelling_correct(self, mock_post):
        """Test spelling check for correct word."""
        # Mock successful API response with no errors
        mock_response = Mock()
        mock_response.json.return_value = {'matches': []}
        mock_response.raise_for_status.return_value = None
        mock_post.return_value = mock_response
        
        is_correct = check_word_spelling_with_languagetool('correct')
        
        self.assertTrue(is_correct)
        mock_post.assert_called_once_with(
            'https://api.languagetool.org/v2/check',
            data={'text': 'correct', 'language': 'en-US'}
        )
    
    @patch('vocabulary.api_services.requests.post')
    def test_check_word_spelling_incorrect(self, mock_post):
        """Test spelling check for incorrect word."""
        # Mock API response with spelling errors
        mock_response = Mock()
        mock_response.json.return_value = {
            'matches': [
                {
                    'message': 'Possible spelling mistake found.',
                    'shortMessage': 'Spelling mistake',
                    'offset': 0,
                    'length': 8,
                    'replacements': [{'value': 'incorrect'}]
                }
            ]
        }
        mock_response.raise_for_status.return_value = None
        mock_post.return_value = mock_response
        
        is_correct = check_word_spelling_with_languagetool('incorect')
        
        self.assertFalse(is_correct)
        mock_post.assert_called_once()
    
    @patch('vocabulary.api_services.requests.post')
    def test_check_word_spelling_empty_word(self, mock_post):
        """Test spelling check with empty word."""
        is_correct = check_word_spelling_with_languagetool('')
        
        self.assertFalse(is_correct)
        mock_post.assert_not_called()
    
    @patch('vocabulary.api_services.requests.post')
    def test_check_word_spelling_none_word(self, mock_post):
        """Test spelling check with None word."""
        is_correct = check_word_spelling_with_languagetool(None)
        
        self.assertFalse(is_correct)
        mock_post.assert_not_called()
    
    @patch('vocabulary.api_services.requests.post')
    def test_check_word_spelling_request_exception(self, mock_post):
        """Test spelling check with request exception."""
        mock_post.side_effect = requests.exceptions.RequestException("Network error")

        with suppress_api_logs():
            is_correct = check_word_spelling_with_languagetool('test')

        self.assertFalse(is_correct)
        mock_post.assert_called_once()
    
    @patch('vocabulary.api_services.requests.post')
    def test_check_word_spelling_http_error(self, mock_post):
        """Test spelling check with HTTP error."""
        mock_response = Mock()
        mock_response.raise_for_status.side_effect = requests.exceptions.HTTPError("500 Server Error")
        mock_post.return_value = mock_response

        with suppress_api_logs():
            is_correct = check_word_spelling_with_languagetool('test')

        self.assertFalse(is_correct)
        mock_post.assert_called_once()
    
    @patch('vocabulary.api_services.requests.post')
    def test_check_word_spelling_json_decode_error(self, mock_post):
        """Test spelling check with JSON decode error."""
        mock_response = Mock()
        mock_response.raise_for_status.return_value = None
        mock_response.json.side_effect = ValueError("Invalid JSON")
        mock_post.return_value = mock_response

        with suppress_api_logs():
            is_correct = check_word_spelling_with_languagetool('test')

        self.assertFalse(is_correct)
        mock_post.assert_called_once()
    
    @patch('vocabulary.api_services.requests.post')
    def test_check_word_spelling_missing_matches_key(self, mock_post):
        """Test spelling check with missing matches key in response."""
        mock_response = Mock()
        mock_response.json.return_value = {'software': {'name': 'LanguageTool'}}
        mock_response.raise_for_status.return_value = None
        mock_post.return_value = mock_response
        
        is_correct = check_word_spelling_with_languagetool('test')
        
        self.assertTrue(is_correct)  # Should default to True when matches key is missing
        mock_post.assert_called_once()


class APIServicesIntegrationTest(TestCase):
    """Integration tests for API services."""
    
    @patch('vocabulary.api_services.requests.get')
    @patch('vocabulary.api_services.requests.post')
    def test_combined_api_usage(self, mock_post, mock_get):
        """Test using both APIs together."""
        # Mock Datamuse API
        mock_get_response = Mock()
        mock_get_response.json.return_value = [
            {'word': 'beautiful', 'score': 100},
            {'word': 'beatiful', 'score': 80}  # Misspelled
        ]
        mock_get_response.raise_for_status.return_value = None
        mock_get.return_value = mock_get_response
        
        # Mock LanguageTool API for correct word
        mock_post_response_correct = Mock()
        mock_post_response_correct.json.return_value = {'matches': []}
        mock_post_response_correct.raise_for_status.return_value = None
        
        # Mock LanguageTool API for incorrect word
        mock_post_response_incorrect = Mock()
        mock_post_response_incorrect.json.return_value = {
            'matches': [{'message': 'Spelling error'}]
        }
        mock_post_response_incorrect.raise_for_status.return_value = None
        
        mock_post.side_effect = [mock_post_response_correct, mock_post_response_incorrect]
        
        # Get suggestions
        suggestions = get_word_suggestions_from_datamuse('beaut')
        self.assertEqual(len(suggestions), 2)
        
        # Check spelling for each suggestion
        is_correct_1 = check_word_spelling_with_languagetool(suggestions[0])
        is_correct_2 = check_word_spelling_with_languagetool(suggestions[1])
        
        self.assertTrue(is_correct_1)   # 'beautiful' should be correct
        self.assertFalse(is_correct_2)  # 'beatiful' should be incorrect
        
        # Verify API calls
        mock_get.assert_called_once()
        self.assertEqual(mock_post.call_count, 2)


class APIServicesEdgeCasesTest(TestCase):
    """Test edge cases and error conditions."""
    
    def test_datamuse_with_special_characters(self):
        """Test Datamuse API with special characters in query."""
        with patch('vocabulary.api_services.requests.get') as mock_get:
            mock_response = Mock()
            mock_response.json.return_value = []
            mock_response.raise_for_status.return_value = None
            mock_get.return_value = mock_response
            
            suggestions = get_word_suggestions_from_datamuse('test@#$%')
            
            self.assertEqual(suggestions, [])
            mock_get.assert_called_once_with('https://api.datamuse.com/sug?s=test@#$%')
    
    def test_languagetool_with_special_characters(self):
        """Test LanguageTool API with special characters in word."""
        with patch('vocabulary.api_services.requests.post') as mock_post:
            mock_response = Mock()
            mock_response.json.return_value = {'matches': []}
            mock_response.raise_for_status.return_value = None
            mock_post.return_value = mock_response
            
            is_correct = check_word_spelling_with_languagetool('test@#$%')
            
            self.assertTrue(is_correct)
            mock_post.assert_called_once_with(
                'https://api.languagetool.org/v2/check',
                data={'text': 'test@#$%', 'language': 'en-US'}
            )
    
    def test_datamuse_with_unicode_characters(self):
        """Test Datamuse API with Unicode characters."""
        with patch('vocabulary.api_services.requests.get') as mock_get:
            mock_response = Mock()
            mock_response.json.return_value = []
            mock_response.raise_for_status.return_value = None
            mock_get.return_value = mock_response
            
            suggestions = get_word_suggestions_from_datamuse('café')
            
            self.assertEqual(suggestions, [])
            mock_get.assert_called_once_with('https://api.datamuse.com/sug?s=café')
    
    def test_languagetool_with_unicode_characters(self):
        """Test LanguageTool API with Unicode characters."""
        with patch('vocabulary.api_services.requests.post') as mock_post:
            mock_response = Mock()
            mock_response.json.return_value = {'matches': []}
            mock_response.raise_for_status.return_value = None
            mock_post.return_value = mock_response
            
            is_correct = check_word_spelling_with_languagetool('café')
            
            self.assertTrue(is_correct)
            mock_post.assert_called_once_with(
                'https://api.languagetool.org/v2/check',
                data={'text': 'café', 'language': 'en-US'}
            )
