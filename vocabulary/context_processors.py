from django.utils import translation
from django.conf import settings
import json

# Only import gettext if i18n is enabled
if getattr(settings, 'USE_I18N', False):
    from django.utils.translation import gettext as _
else:
    # Fallback function when i18n is disabled
    def _(message):
        return str(message)

def i18n_compatible_translations(request):
    """
    Hybrid translation context processor that provides both Django i18n
    and legacy manual_texts for backward compatibility during migration.
    """
    # Get language from session first (for legacy system), then fall back to Django's i18n
    current_lang = request.session.get('django_language', translation.get_language())

    # If Django i18n is disabled, default to 'en'
    if not current_lang:
        current_lang = 'en'

    # Legacy translations for backward compatibility
    # These will be gradually removed as templates are migrated to use {% trans %}
    legacy_translations = {
        'en': {
            'learn_english': 'Learn English',
            'home': 'Home',
            'flashcards': 'Flashcards',
            'add_word': 'Add Word',
            'study': 'Study',
            'statistics': 'Statistics',
            'dashboard': 'Dashboard',
            'welcome_message': 'Welcome to LearnEnglish',
            'platform_description': 'Your personal vocabulary learning platform',
            'total_cards': 'Total Cards',
            'recent_cards': 'Recent Cards',
            'progress': 'Progress',
            'add_new_words': 'Add New Words',
            'study_cards': 'Study Cards',
            'add_flashcard': 'Add Flashcard',
            'add_new_flashcard': 'Add New Flashcard',
            'add_vocabulary_description': 'Add new vocabulary to your collection',
            'my_flashcards': 'My Flashcards',
            'vocabulary_collection': 'Your vocabulary collection',
            # Authentication texts
            'login_title': 'Login',
            'signup_title': 'Sign Up',
            'welcome_back': 'Welcome back!',
            'login_subtitle': 'Sign in to your account',
            'create_account': 'Create Account',
            'signup_subtitle': 'Start your vocabulary learning journey',
            'login_with_google': 'Sign in with Google',
            'signup_with_google': 'Sign up with Google',
            'or': 'or',
            'email_address': 'Email address',
            'password': 'Password',
            'confirm_password': 'Confirm password',
            'remember_me': 'Remember me',
            'forgot_password': 'Forgot password?',
            'sign_in': 'Sign in',
            'sign_up': 'Sign up',
            'dont_have_account': "Don't have an account?",
            'already_have_account': 'Already have an account?',
            'email_verification_notice': 'We will send you a verification email to activate your account.',
            'logout': 'Logout',
            'profile': 'Profile',
            # Decks
            'my_decks': 'My Decks',
            'back_to_all_decks': 'Back to all decks',
            # Statistics
            'statistics_title': 'Statistics',
            'total_decks': 'Total Decks',
            'average_cards_per_deck': 'Average Cards per Deck',
            'cards_per_deck': 'Cards per Deck',
            'no_decks_message': 'You have no decks yet.',
            # Study labels
            'start_study': 'Start Study',
            'select_decks': 'Select decks to study',
            'no_cards_due': 'No cards due for review today.',
            'grade_again': 'Again',
            'grade_hard': 'Hard',
            'grade_good': 'Good',
            'grade_easy': 'Easy',
            'show_answer': 'Show Answer',
            'mode_multiple_choice': 'Multiple Choice',
            'mode_type_answer': 'Type Answer',
            'select_mode': 'Select study mode',
            'check': 'Check',
            'correct': 'Correct!',
            'incorrect': 'Incorrect',
            'answer_placeholder': 'Type your answer...',
            'study_complete_title': 'Great job!',
            'study_complete_text': 'You have reviewed all due cards.',
            'next_card': 'Next Card →',
            'vietnamese_meaning': 'Vietnamese meaning',
            'dictation_placeholder': 'Listen and type the English word...',
            'select_at_least_one_deck': 'Please select at least one deck',
            # Add flashcard form
            'select_deck': 'Select deck:',
            'please_select_deck': '-- Please select a deck --',
            'create_new_deck': '-- Create new deck --',
            'term_label': 'TERM',
            'phonetic_label': 'PHONETIC',
            'english_definition_label': 'DEFINITION (ENGLISH)',
            'vietnamese_definition_label': 'DEFINITION (VIETNAMESE)',
            'term_placeholder': 'e.g., \'serendipity\'',
            'phonetic_placeholder': '/ˌser.ənˈdɪp.ə.t̬i/',
            'definition_placeholder': 'A concise and clear definition...',
            'vietnamese_placeholder': 'Vietnamese translation...',
            'upload_image': 'Upload image',
            'add_new_card': '➕ Add new card',
            'save_all_flashcards': '💾 Save all Flashcards',
            'drag_to_move': 'Drag to move card',
            'delete_card': 'Delete card',
            'part_of_speech': 'part of speech',
            'listen': 'Listen',

            # Quick Add Words section
            'quick_add_multiple_words': 'Quick Add Multiple Words',
            'quick_add_placeholder': 'Enter multiple words separated by | (pipe character). Example: assistant|cry|usual|file|ban|ice|column|currently|prepare|acceptable',
            'quick_add_info': 'Separate words with | (pipe) character. Each word will be automatically processed for spelling, definitions, and duplicates.',
            'generate_cards': 'Generate Cards',
            'processing_words': 'Processing words...',
            'processing_word_individual': 'Processing "{word}" ({current}/{total})...',
            'clearing_existing_cards': 'Clearing existing cards',

            # SweetAlert messages
            'create_new_deck_title': 'Create New Deck',
            'deck_name_label': 'Deck Name',
            'deck_name_placeholder': 'Example: Day 1, IELTS Topic: Work...',
            'deck_name_required': 'You need to enter a name for the deck!',
            'cancel': 'Cancel',
            'created': 'Created!',
            'deck_created_success': 'Deck "{deck_name}" has been created successfully.',
            'cannot_create_deck': 'Cannot create deck',
            'unknown_error': 'Unknown error',

            # Duplicate warnings
            'duplicate_word_detected': 'Duplicate Word Detected',
            'word_already_exists': 'The word "{word}" already exists in your vocabulary.',
            'use_different_word': 'Please use a different word or modify the existing one.',

            # Processing and validation messages
            'no_words_found': 'No Words Found',
            'enter_words_pipe': 'Please enter some words separated by | (pipe) character.',
            'no_deck_selected': 'No Deck Selected',
            'select_deck_before_adding': 'Please select a deck before adding words.',
            'cannot_delete_only_card': 'Cannot delete the only card!',
            'translating': 'Translating...',
            'translation_not_available': 'Translation not available.',
            'translation_error': 'Translation error.',

            # Quick Add results
            'quick_add_results': 'Quick Add Results',
            'words_added_successfully': 'Successfully added {count} words: {words}',
            'duplicate_words_skipped': 'Skipped {count} duplicate words: {words}',
            'words_with_errors': 'Failed to process {count} words: {words}',
            'no_words_processed': 'No words were processed. Please check your input.',

            # Flashcard save success messages
            'saved_successfully': 'Saved successfully!',
            'words_added_to_collection': 'Words have been added to the collection: {words}',

            'correct_answer': 'Correct answer',
            # Console messages
            'console_welcome': '🎓 LearnEnglish App',
            'console_subtitle': 'Welcome to the developer console!',
            'console_built_with': 'Built with Django + Tailwind CSS + JavaScript ❤️',
            # Deck list
            'cards_text': 'cards',
            'no_decks_yet': 'You don\'t have any decks yet.',
            'get_started_by': 'Get started by',
            'adding_flashcards': 'adding some new flashcards',
            # Study
            'search_cambridge': 'Search on Cambridge Dictionary',
            'continue_button': 'Continue',
            'no_decks_selected': 'No decks selected',
            'back_to_login': 'Back to Login',
            'no_decks_available': 'No decks available',
            # Edit functionality
            'edit_card': 'Edit Card',
            'save_changes': 'Save Changes',
            'cancel_edit': 'Cancel',
            'edit_mode': 'Edit Mode',
            'card_updated_successfully': 'Card updated successfully!',
            'error_updating_card': 'Error updating card',
            'confirm_cancel_edit': 'Are you sure you want to cancel? Unsaved changes will be lost.',
            'this_deck_empty': 'This deck is empty.',
            'add_some_cards': 'Add some cards!',
            # Audio status indicators
            'has_audio': 'Has Audio',
            'no_audio': 'No Audio',
            'audio_available': 'Audio Available',
            'audio_missing': 'Audio Missing',
            'add_audio_url': 'Add audio URL to enable pronunciation',
            'filter_by_audio': 'Filter by Audio Status',
            'show_all_cards': 'Show All Cards',
            'show_cards_with_audio': 'Cards with Audio',
            'show_cards_without_audio': 'Cards without Audio',
            # Deck editing
            'edit_deck_name': 'Edit Deck Name',
            'deck_name': 'Deck Name',
            'save_deck_name': 'Save Name',
            'cancel_deck_edit': 'Cancel',
            'deck_name_updated': 'Deck name updated successfully!',
            'error_updating_deck': 'Error updating deck name',
            'deck_name_required': 'Deck name is required',
            # Audio fetching
            'fetch_missing_audio': 'Fetch Missing Audio',
            'fetching_audio': 'Fetching audio...',
            'audio_fetched_successfully': 'Audio fetched successfully!',
            'no_audio_found': 'No audio found for some words',
            'audio_fetch_error': 'Error fetching audio',
            'audio_fetch_complete': 'Audio fetch complete',
            'cards_updated': 'cards updated',
            'auto_fetch_audio': 'Auto-fetch audio for new cards',

            # Enhanced Audio Fetching
            'enhanced_audio_selection': 'Enhanced Audio Selection',
            'select_audio_pronunciation': 'Select Audio Pronunciation for:',
            'available_audio_options': 'Available Audio Options',
            'current_audio': 'Current Audio',
            'no_current_audio': 'No current audio',
            'preview': 'Preview',
            'us_pronunciation': 'US pronunciation',
            'uk_pronunciation': 'UK pronunciation',
            'primary_pronunciation': 'Primary pronunciation',
            'alternative_pronunciation': 'Alternative pronunciation',
            'ready': 'Ready',
            'playing': 'Playing',
            'keep_current': 'Keep Current',
            'confirm_selection': 'Confirm Selection',
            'no_audio_options_found': 'No audio options found',
            'try_checking_spelling': 'Try checking the word spelling or search manually on Cambridge Dictionary',
            'fetching_audio_options': 'Fetching audio options...',
            'audio_selection_updated': 'Audio pronunciation updated successfully!',
            'error_updating_audio': 'Error updating audio selection',
            'please_select_audio': 'Please select an audio option',
            'enhanced_audio_fetch': 'Enhanced Audio Fetch',
            'get_multiple_pronunciations': 'Get Multiple Pronunciations',

            # Study Mode Selection
            'study_mode': 'Study Mode',
            'normal_study_by_decks': 'Normal Study (by Decks)',
            'study_random_words': 'Study Random Words',
            'number_of_words': 'Number of Words',
            'available_words': 'Available Words',
            'select_decks': 'Select Decks',
            'no_decks_selected': 'No decks selected',
            'no_decks_available': 'No decks available',
            'start_study': 'Start Study',
            'correct': 'Correct',
            'incorrect': 'Incorrect',
            'check': 'Check',
            'answer_placeholder': 'Enter your answer...',
            'grade_again': 'Again',
            'grade_hard': 'Hard',
            'grade_good': 'Good',
            'grade_easy': 'Easy',
            'view_in_cambridge': 'View in Cambridge Dictionary',
            'no_cards_due': 'No cards due',
            'back_to_selection': 'Back to Selection',
            # Audio and study interface
            'play_audio': 'Play Audio',
            'listen_and_type': 'Listen and type what you hear',
            'type_what_you_hear': 'Type what you hear...',
            'correct_answer': 'Correct!',
            'incorrect_answer': 'Incorrect',
            'replay_audio': 'Replay',
            'english_label': 'English:',
            'vietnamese_label': 'Vietnamese:',

            # Deck detail interface
            'word_required': 'Word is required',
            'definition_required': 'At least one definition is required',
            'saving': 'Saving...',
            'previous_deck': 'Previous deck',
            'next_deck': 'Next deck',
            'card_updated_successfully': 'Card updated successfully!',
            'error_updating_card': 'Error updating card',
            'confirm_cancel_edit': 'Are you sure you want to cancel? Unsaved changes will be lost.',
            'deck_name_required': 'Deck name is required',
            'deck_name_updated': 'Deck name updated successfully!',
            'error_updating_deck': 'Error updating deck name',
            'save_deck_name': 'Save Name',
            'fetching_audio': 'Fetching audio...',
            'audio_fetched_successfully': 'Audio fetched successfully!',
            'cards_updated': 'cards updated',
            'no_audio_found': 'No audio found for some words',
            'audio_fetch_error': 'Error fetching audio',
            'audio_fetch_complete': 'Audio fetch complete',
            'found_label': 'Found:',
            'method_not_allowed': 'Method not allowed. Please refresh the page and try again.',
            'permission_denied': 'Permission denied. Please refresh the page and try again.',
            'card_not_found': 'Card not found. Please refresh the page and try again.',
            'server_response_error': 'Server response error. Please try again.',
            'no_definitions_available': 'No definitions available',
            'audio_url_label': 'Audio URL',
            'audio_url_placeholder': 'https://example.com/audio.mp3',
            'definitions_label': 'Definitions',
            'signed_in_as': 'Signed in as',
            'select_deck_alert': 'Please select at least one deck to study.',
            'no_decks_selected': 'No decks selected',
            'words_unit': 'words',
            'deck_study_description': 'Study specific flashcard decks',
            'random_study_description': 'Study random words from all decks',
            'sound_feedback': 'Sound Feedback',
            'review_incorrect_words': 'Review Incorrect Words',
            'review_study_description': 'Study words you previously answered incorrectly',
            'incorrect_words_count': 'incorrect words to review',
            'review_mode_description': 'Practice words you answered incorrectly in their original question format until you master them.',
            'start_review': 'Start Review',
            'multiple_choice': 'Multiple Choice',
            'input_mode': 'Input Mode',
            'dictation_mode': 'Dictation Mode',
            'review_completed_title': 'Congratulations!',
            'review_completed_message': 'You have successfully reviewed all incorrect words!',
            'continue_studying': 'Continue Studying',
        },
        'vi': {
            'learn_english': 'Học Tiếng Anh',
            'home': 'Trang chủ',
            'flashcards': 'Thẻ từ vựng',
            'add_word': 'Thêm từ',
            'study': 'Học tập',
            'statistics': 'Thống kê',
            'dashboard': 'Bảng điều khiển',
            'welcome_message': 'Chào mừng đến với LearnEnglish',
            'platform_description': 'Nền tảng học từ vựng cá nhân của bạn',
            'total_cards': 'Tổng số thẻ',
            'recent_cards': 'Thẻ gần đây',
            'progress': 'Tiến độ',
            'add_new_words': 'Thêm từ mới',
            'study_cards': 'Học thẻ từ',
            'add_flashcard': 'Thêm Flashcard',
            'add_new_flashcard': 'Thêm Flashcard Mới',
            'add_vocabulary_description': 'Thêm từ vựng mới vào bộ sưu tập của bạn',
            'my_flashcards': 'Thẻ từ vựng của tôi',
            'vocabulary_collection': 'Bộ sưu tập từ vựng của bạn',
            # Authentication texts
            'login_title': 'Đăng nhập',
            'signup_title': 'Đăng ký',
            'welcome_back': 'Chào mừng trở lại!',
            'login_subtitle': 'Đăng nhập vào tài khoản của bạn',
            'create_account': 'Tạo tài khoản',
            'signup_subtitle': 'Bắt đầu hành trình học từ vựng',
            'login_with_google': 'Đăng nhập với Google',
            'signup_with_google': 'Đăng ký với Google',
            'or': 'hoặc',
            'email_address': 'Địa chỉ email',
            'password': 'Mật khẩu',
            'confirm_password': 'Xác nhận mật khẩu',
            'remember_me': 'Ghi nhớ đăng nhập',
            'forgot_password': 'Quên mật khẩu?',
            'sign_in': 'Đăng nhập',
            'sign_up': 'Đăng ký',
            'dont_have_account': 'Chưa có tài khoản?',
            'already_have_account': 'Đã có tài khoản?',
            'email_verification_notice': 'Chúng tôi sẽ gửi email xác thực để kích hoạt tài khoản của bạn.',
            'logout': 'Đăng xuất',
            'profile': 'Hồ sơ',
            # Decks
            'my_decks': 'Các bộ thẻ',
            'back_to_all_decks': 'Quay lại tất cả bộ thẻ',
            # Statistics
            'statistics_title': 'Thống kê',
            'total_decks': 'Tổng số bộ thẻ',
            'average_cards_per_deck': 'Số thẻ trung bình mỗi bộ',
            'cards_per_deck': 'Số thẻ trong mỗi bộ',
            'no_decks_message': 'Bạn chưa có bộ thẻ nào.',
            # Study labels
            'start_study': 'Bắt đầu ôn',
            'select_decks': 'Chọn bộ thẻ để ôn',
            'no_cards_due': 'Hôm nay không có thẻ cần ôn.',
            'grade_again': 'Làm lại',
            'grade_hard': 'Khó',
            'grade_good': 'Tốt',
            'grade_easy': 'Dễ',
            'show_answer': 'Hiện đáp án',
            'mode_multiple_choice': 'Trắc nghiệm',
            'mode_type_answer': 'Gõ đáp án',
            'select_mode': 'Chọn chế độ học',
            'check': 'Kiểm tra',
            'correct': 'Chính xác!',
            'incorrect': 'Chưa đúng',
            'answer_placeholder': 'Nhập đáp án...',
            'study_complete_title': 'Hoàn thành!',
            'study_complete_text': 'Bạn đã ôn xong tất cả thẻ cần ôn.',
            'next_card': 'Thẻ tiếp theo →',
            'vietnamese_meaning': 'Nghĩa tiếng Việt',
            'dictation_placeholder': 'Nghe và nhập từ tiếng Anh...',
            'select_at_least_one_deck': 'Vui lòng chọn ít nhất một bộ thẻ',
            # Add flashcard form
            'select_deck': 'Chọn bộ thẻ:',
            'please_select_deck': '-- Vui lòng chọn một bộ thẻ --',
            'create_new_deck': '-- Tạo bộ thẻ mới --',
            'term_label': 'THUẬT NGỮ',
            'phonetic_label': 'PHÁT ÂM',
            'english_definition_label': 'ĐỊNH NGHĨA (ENGLISH)',
            'vietnamese_definition_label': 'ĐỊNH NGHĨA (VIETNAMESE)',
            'term_placeholder': 'ví dụ: \'serendipity\'',
            'phonetic_placeholder': '/ˌser.ənˈdɪp.ə.t̬i/',
            'definition_placeholder': 'Định nghĩa rõ ràng và ngắn gọn...',
            'vietnamese_placeholder': 'Bản dịch tiếng Việt...',
            'upload_image': 'Tải ảnh lên',
            'add_new_card': '➕ Thêm thẻ mới',
            'save_all_flashcards': '💾 Lưu tất cả Flashcards',
            'drag_to_move': 'Kéo để di chuyển thẻ',
            'delete_card': 'Xóa thẻ',
            'part_of_speech': 'từ loại',
            'listen': 'Nghe',

            # Quick Add Words section
            'quick_add_multiple_words': 'Thêm nhanh nhiều từ',
            'quick_add_placeholder': 'Nhập nhiều từ cách nhau bằng ký tự | (pipe). Ví dụ: assistant|cry|usual|file|ban|ice|column|currently|prepare|acceptable',
            'quick_add_info': 'Phân tách các từ bằng ký tự | (pipe). Mỗi từ sẽ được tự động xử lý về chính tả, định nghĩa và trùng lặp.',
            'generate_cards': 'Tạo thẻ',
            'processing_words': 'Đang xử lý từ...',
            'processing_word_individual': 'Đang xử lý "{word}" ({current}/{total})...',
            'clearing_existing_cards': 'Đang xóa các thẻ hiện có',

            # SweetAlert messages
            'create_new_deck_title': 'Tạo bộ thẻ mới',
            'deck_name_label': 'Tên bộ thẻ',
            'deck_name_placeholder': 'Ví dụ: Ngày 1, IELTS Topic: Work...',
            'deck_name_required': 'Bạn cần nhập tên cho bộ thẻ!',
            'cancel': 'Hủy',
            'created': 'Đã tạo!',
            'deck_created_success': 'Bộ thẻ "{deck_name}" đã được tạo thành công.',
            'cannot_create_deck': 'Không thể tạo bộ thẻ',
            'unknown_error': 'Lỗi không xác định',

            # Duplicate warnings
            'duplicate_word_detected': 'Phát hiện từ trùng lặp',
            'word_already_exists': 'Từ "{word}" đã tồn tại trong từ vựng của bạn.',
            'use_different_word': 'Vui lòng sử dụng từ khác hoặc chỉnh sửa từ hiện có.',

            # Processing and validation messages
            'no_words_found': 'Không tìm thấy từ nào',
            'enter_words_pipe': 'Vui lòng nhập một số từ cách nhau bằng ký tự | (pipe).',
            'no_deck_selected': 'Chưa chọn bộ thẻ',
            'select_deck_before_adding': 'Vui lòng chọn một bộ thẻ trước khi thêm từ.',
            'cannot_delete_only_card': 'Không thể xóa thẻ duy nhất!',
            'translating': 'Đang dịch...',
            'translation_not_available': 'Bản dịch không có sẵn.',
            'translation_error': 'Lỗi dịch thuật.',

            # Quick Add results
            'quick_add_results': 'Kết quả thêm nhanh',
            'words_added_successfully': 'Đã thêm thành công {count} từ: {words}',
            'duplicate_words_skipped': 'Đã bỏ qua {count} từ trùng lặp: {words}',
            'words_with_errors': 'Không thể xử lý {count} từ: {words}',
            'no_words_processed': 'Không có từ nào được xử lý. Vui lòng kiểm tra đầu vào.',

            # Flashcard save success messages
            'saved_successfully': 'Đã lưu thành công!',
            'words_added_to_collection': 'Các từ đã được thêm vào bộ sưu tập: {words}',

            'correct_answer': 'Đáp án',
            # Console messages
            'console_welcome': '🎓 Ứng dụng LearnEnglish',
            'console_subtitle': 'Chào mừng đến với console phát triển!',
            'console_built_with': 'Được xây dựng với Django + Tailwind CSS + JavaScript ❤️',
            # Deck list
            'cards_text': 'thẻ',
            'no_decks_yet': 'Bạn chưa có bộ thẻ nào.',
            'get_started_by': 'Bắt đầu bằng cách',
            'adding_flashcards': 'thêm một số flashcard mới',
            # Study
            'search_cambridge': 'Tìm kiếm trên Cambridge Dictionary',
            'continue_button': 'Tiếp tục',
            'no_decks_selected': 'Chưa chọn bộ thẻ nào',
            'back_to_login': 'Quay lại Đăng nhập',
            'no_decks_available': 'Không có bộ thẻ nào',
            # Edit functionality
            'edit_card': 'Chỉnh sửa thẻ',
            'save_changes': 'Lưu thay đổi',
            'cancel_edit': 'Hủy',
            'edit_mode': 'Chế độ chỉnh sửa',
            'card_updated_successfully': 'Cập nhật thẻ thành công!',
            'error_updating_card': 'Lỗi khi cập nhật thẻ',
            'confirm_cancel_edit': 'Bạn có chắc muốn hủy? Các thay đổi chưa lưu sẽ bị mất.',
            'this_deck_empty': 'Bộ thẻ này trống.',
            'add_some_cards': 'Thêm một số thẻ!',
            # Audio status indicators
            'has_audio': 'Có âm thanh',
            'no_audio': 'Không có âm thanh',
            'audio_available': 'Có âm thanh',
            'audio_missing': 'Thiếu âm thanh',
            'add_audio_url': 'Thêm URL âm thanh để bật phát âm',
            'filter_by_audio': 'Lọc theo trạng thái âm thanh',
            'show_all_cards': 'Hiển thị tất cả thẻ',
            'show_cards_with_audio': 'Thẻ có âm thanh',
            'show_cards_without_audio': 'Thẻ không có âm thanh',
            # Deck editing
            'edit_deck_name': 'Chỉnh sửa tên bộ thẻ',
            'deck_name': 'Tên bộ thẻ',
            'save_deck_name': 'Lưu tên',
            'cancel_deck_edit': 'Hủy',
            'deck_name_updated': 'Cập nhật tên bộ thẻ thành công!',
            'error_updating_deck': 'Lỗi khi cập nhật tên bộ thẻ',
            'deck_name_required': 'Tên bộ thẻ là bắt buộc',
            # Audio fetching
            'fetch_missing_audio': 'Lấy âm thanh thiếu',
            'fetching_audio': 'Đang lấy âm thanh...',
            'audio_fetched_successfully': 'Lấy âm thanh thành công!',
            'no_audio_found': 'Không tìm thấy âm thanh cho một số từ',
            'audio_fetch_error': 'Lỗi khi lấy âm thanh',
            'audio_fetch_complete': 'Hoàn thành lấy âm thanh',
            'cards_updated': 'thẻ đã cập nhật',
            'auto_fetch_audio': 'Tự động lấy âm thanh cho thẻ mới',

            # Enhanced Audio Fetching
            'enhanced_audio_selection': 'Chọn âm thanh nâng cao',
            'select_audio_pronunciation': 'Chọn cách phát âm cho:',
            'available_audio_options': 'Tùy chọn âm thanh có sẵn',
            'current_audio': 'Âm thanh hiện tại',
            'no_current_audio': 'Không có âm thanh hiện tại',
            'preview': 'Nghe thử',
            'us_pronunciation': 'Phát âm Mỹ',
            'uk_pronunciation': 'Phát âm Anh',
            'primary_pronunciation': 'Phát âm chính',
            'alternative_pronunciation': 'Phát âm thay thế',
            'ready': 'Sẵn sàng',
            'playing': 'Đang phát',
            'keep_current': 'Giữ nguyên',
            'confirm_selection': 'Xác nhận lựa chọn',
            'no_audio_options_found': 'Không tìm thấy tùy chọn âm thanh',
            'try_checking_spelling': 'Thử kiểm tra chính tả hoặc tìm kiếm thủ công trên Cambridge Dictionary',
            'fetching_audio_options': 'Đang tìm tùy chọn âm thanh...',
            'audio_selection_updated': 'Cập nhật cách phát âm thành công!',
            'error_updating_audio': 'Lỗi khi cập nhật lựa chọn âm thanh',
            'please_select_audio': 'Vui lòng chọn một tùy chọn âm thanh',
            'enhanced_audio_fetch': 'Lấy âm thanh nâng cao',
            'get_multiple_pronunciations': 'Lấy nhiều cách phát âm',

            # Study Mode Selection
            'study_mode': 'Chế độ học tập',
            'normal_study_by_decks': 'Học bình thường (theo bộ thẻ)',
            'study_random_words': 'Học từ ngẫu nhiên',
            'number_of_words': 'Số lượng từ',
            'available_words': 'Từ có sẵn',
            'select_decks': 'Chọn bộ thẻ',
            'no_decks_selected': 'Chưa chọn bộ thẻ nào',
            'no_decks_available': 'Không có bộ thẻ nào',
            'start_study': 'Bắt đầu học',
            'correct': 'Đúng',
            'incorrect': 'Sai',
            'check': 'Kiểm tra',
            'answer_placeholder': 'Nhập đáp án của bạn...',
            'grade_again': 'Lại',
            'grade_hard': 'Khó',
            'grade_good': 'Tốt',
            'grade_easy': 'Dễ',
            'view_in_cambridge': 'Xem trong Cambridge Dictionary',
            'no_cards_due': 'Không có thẻ nào cần học',
            'back_to_selection': 'Quay lại lựa chọn',
            # Audio and study interface
            'play_audio': 'Phát âm',
            'listen_and_type': 'Nghe và viết những gì bạn nghe được',
            'type_what_you_hear': 'Viết những gì bạn nghe được...',
            'correct_answer': 'Đúng!',
            'incorrect_answer': 'Sai',
            'replay_audio': 'Nghe lại',
            'english_label': 'Tiếng Anh:',
            'vietnamese_label': 'Tiếng Việt:',

            # Deck detail interface
            'word_required': 'Từ vựng là bắt buộc',
            'definition_required': 'Cần ít nhất một định nghĩa',
            'saving': 'Đang lưu...',
            'previous_deck': 'Bộ thẻ trước',
            'next_deck': 'Bộ thẻ tiếp theo',
            'card_updated_successfully': 'Cập nhật thẻ thành công!',
            'error_updating_card': 'Lỗi khi cập nhật thẻ',
            'confirm_cancel_edit': 'Bạn có chắc muốn hủy? Các thay đổi chưa lưu sẽ bị mất.',
            'deck_name_required': 'Tên bộ thẻ là bắt buộc',
            'deck_name_updated': 'Cập nhật tên bộ thẻ thành công!',
            'error_updating_deck': 'Lỗi khi cập nhật tên bộ thẻ',
            'save_deck_name': 'Lưu Tên',
            'fetching_audio': 'Đang tải âm thanh...',
            'audio_fetched_successfully': 'Tải âm thanh thành công!',
            'cards_updated': 'thẻ đã được cập nhật',
            'no_audio_found': 'Không tìm thấy âm thanh cho một số từ',
            'audio_fetch_error': 'Lỗi khi tải âm thanh',
            'audio_fetch_complete': 'Hoàn thành tải âm thanh',
            'found_label': 'Tìm thấy:',
            'method_not_allowed': 'Phương thức không được phép. Vui lòng làm mới trang và thử lại.',
            'permission_denied': 'Không có quyền truy cập. Vui lòng làm mới trang và thử lại.',
            'card_not_found': 'Không tìm thấy thẻ. Vui lòng làm mới trang và thử lại.',
            'server_response_error': 'Lỗi phản hồi từ máy chủ. Vui lòng thử lại.',
            'no_definitions_available': 'Không có định nghĩa nào',
            'audio_url_label': 'URL Âm thanh',
            'audio_url_placeholder': 'https://example.com/audio.mp3',
            'definitions_label': 'Định nghĩa',
            'signed_in_as': 'Đã đăng nhập với tư cách',
            'select_deck_alert': 'Vui lòng chọn ít nhất một bộ thẻ để học.',
            'no_decks_selected': 'Chưa chọn bộ thẻ nào',
            'words_unit': 'từ',
            'deck_study_description': 'Học theo từng bộ thẻ cụ thể',
            'random_study_description': 'Học từ ngẫu nhiên từ tất cả bộ thẻ',
            'sound_feedback': 'Âm thanh phản hồi',
            'review_incorrect_words': 'Ôn lại từ đã sai',
            'review_study_description': 'Học lại những từ bạn đã trả lời sai trước đây',
            'incorrect_words_count': 'từ sai cần ôn lại',
            'review_mode_description': 'Luyện tập những từ bạn đã trả lời sai theo định dạng câu hỏi gốc cho đến khi thành thạo.',
            'start_review': 'Bắt đầu ôn tập',
            'multiple_choice': 'Trắc nghiệm',
            'input_mode': 'Nhập từ',
            'dictation_mode': 'Chính tả',
            'review_completed_title': 'Chúc mừng!',
            'review_completed_message': 'Bạn đã ôn tập thành công tất cả các từ sai!',
            'continue_studying': 'Tiếp tục học',
        }
    }
    
    # Create a hybrid system that provides both legacy and Django i18n
    legacy_texts = legacy_translations.get(current_lang, legacy_translations['en'])

    # JavaScript translations using Django's gettext (fallback to English if translation fails)
    js_translations = {}
    try:
        js_translations = {
            'console_welcome': str(_("🎓 LearnEnglish App")),
            'console_subtitle': str(_("Welcome to the developer console!")),
            'console_built_with': str(_("Built with Django + Tailwind CSS + JavaScript ❤️")),
            'home': str(_("Home")),
            'flashcards': str(_("Flashcards")),
            'add_word': str(_("Add Word")),
            'study': str(_("Study")),
            'statistics': str(_("Statistics")),
            'profile': str(_("Profile")),
            'logout': str(_("Logout")),
            'correct': str(_("Correct")),
            'incorrect': str(_("Incorrect")),
            'check': str(_("Check")),
            'saving': str(_("Saving...")),
            'loading': str(_("Loading...")),
            'error': str(_("Error")),
            'success': str(_("Success")),
        }
    except Exception:
        # Fallback to legacy translations if Django i18n fails
        js_translations = {
            'console_welcome': legacy_texts.get('console_welcome', '🎓 LearnEnglish App'),
            'console_subtitle': legacy_texts.get('console_subtitle', 'Welcome to the developer console!'),
            'console_built_with': legacy_texts.get('console_built_with', 'Built with Django + Tailwind CSS + JavaScript ❤️'),
            'home': legacy_texts.get('home', 'Home'),
            'flashcards': legacy_texts.get('flashcards', 'Flashcards'),
            'add_word': legacy_texts.get('add_word', 'Add Word'),
            'study': legacy_texts.get('study', 'Study'),
            'statistics': legacy_texts.get('statistics', 'Statistics'),
            'profile': legacy_texts.get('profile', 'Profile'),
            'logout': legacy_texts.get('logout', 'Logout'),
            'correct': legacy_texts.get('correct', 'Correct'),
            'incorrect': legacy_texts.get('incorrect', 'Incorrect'),
            'check': legacy_texts.get('check', 'Check'),
            'saving': legacy_texts.get('saving', 'Saving...'),
            'loading': legacy_texts.get('loading', 'Loading...'),
            'error': legacy_texts.get('error', 'Error'),
            'success': legacy_texts.get('success', 'Success'),
        }

    # For templates that haven't been migrated yet, provide manual_texts
    # For new templates, they should use {% trans %} tags directly
    return {
        'manual_texts': legacy_texts,  # Legacy support
        'current_language_code': current_lang,
        'js_translations_json': json.dumps(js_translations),  # JSON string for JavaScript
    }

def manual_translations(request):
    """
    DEPRECATED: Use i18n_compatible_translations instead.
    This function is kept for backward compatibility only.
    """
    return i18n_compatible_translations(request)