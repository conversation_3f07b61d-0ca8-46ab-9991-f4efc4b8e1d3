{% extends "base.html" %}
{% load i18n_compat %}

{% block title %}{% trans "Dashboard" %} - {% trans "Learn English" %}{% endblock %}

{% block content %}
<div class="dashboard-container">
  <div class="welcome-section">
    <h1>{% trans "Welcome to LearnEnglish" %}</h1>
    <p>{% trans "Your personal vocabulary learning platform" %}</p>
  </div>

  <div class="stats-grid">
    <div class="stat-card">
      <div class="stat-icon">📚</div>
      <div class="stat-content">
        <div class="stat-number">{{ total_cards }}</div>
        <div class="stat-label">{% trans "Total Cards" %}</div>
      </div>
    </div>

    <div class="stat-card">
      <div class="stat-icon">🆕</div>
      <div class="stat-content">
        <div class="stat-number">{{ recent_cards }}</div>
        <div class="stat-label">{% trans "Recent Cards" %}</div>
      </div>
    </div>

    <div class="stat-card">
      <div class="stat-icon">📊</div>
      <div class="stat-content">
        <div class="stat-number">{{ progress_percentage|floatformat:0 }}%</div>
        <div class="stat-label">{% trans "Progress" %}</div>
      </div>
    </div>
  </div>

  <div class="action-buttons">
    <a href="{% url 'add_flashcard' %}" class="action-btn primary">
      <span class="btn-icon">➕</span>
      <span>{% trans "Add New Words" %}</span>
    </a>

    <a href="{% url 'deck_list' %}" class="action-btn secondary">
      <span class="btn-icon">📖</span>
      <span>{% trans "Study Cards" %}</span>
    </a>
  </div>
</div>

<style>
  .dashboard-container {
    max-width: 1000px;
    margin: 0 auto;
    padding: 20px;
  }

  .welcome-section {
    text-align: center;
    margin-bottom: 40px;
  }

  .welcome-section h1 {
    font-size: 2.5em;
    margin-bottom: 10px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  .welcome-section p {
    color: #b0b0b0;
    font-size: 1.1em;
  }

  .stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 40px;
  }

  .stat-card {
    background: #232345;
    border-radius: 12px;
    padding: 24px;
    display: flex;
    align-items: center;
    gap: 16px;
    border: 1px solid #3a3a5c;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
  }

  .stat-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.15);
  }

  .stat-icon {
    font-size: 2em;
    opacity: 0.8;
  }

  .stat-content {
    flex: 1;
  }

  .stat-number {
    font-size: 2em;
    font-weight: bold;
    color: #667eea;
    line-height: 1;
  }

  .stat-label {
    color: #b0b0b0;
    font-size: 0.9em;
    margin-top: 4px;
  }

  .action-buttons {
    display: flex;
    gap: 20px;
    justify-content: center;
    flex-wrap: wrap;
  }

  .action-btn {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 16px 28px;
    border-radius: 12px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
    font-size: 1.1em;
  }

  .action-btn.primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
  }

  .action-btn.primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
  }

  .action-btn.secondary {
    background: #232345;
    color: #b0b0b0;
    border: 2px solid #3a3a5c;
  }

  .action-btn.secondary:hover {
    background: #3a3a5c;
    color: white;
    transform: translateY(-2px);
  }

  .btn-icon {
    font-size: 1.2em;
  }

  @media (max-width: 768px) {
    .welcome-section h1 {
      font-size: 2em;
    }

    .stats-grid {
      grid-template-columns: 1fr;
    }

    .action-buttons {
      flex-direction: column;
    }

    .action-btn {
      justify-content: center;
    }
  }
</style>
{% endblock %}
