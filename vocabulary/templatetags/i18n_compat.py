"""
Custom template tags for i18n compatibility.
Provides {% trans %} functionality even when Django's USE_I18N = False.
"""
from django import template
from django.template.defaulttags import token_kwargs
from django.utils.safestring import mark_safe
from vocabulary.context_processors import get_translation

register = template.Library()


@register.simple_tag(takes_context=True)
def trans(context, message, **kwargs):
    """
    Custom trans tag that works like Django's {% trans %} but uses our manual translation system.
    
    Usage:
        {% load i18n_compat %}
        {% trans "Hello World" %}
        {% trans "Hello" as greeting %}
    """
    # Get current language from context
    current_lang = context.get('current_language_code', 'en')
    
    # Get translation
    translated = get_translation(message, current_lang)
    
    # Handle 'as' variable assignment
    if 'as' in kwargs:
        context[kwargs['as']] = translated
        return ''
    
    return mark_safe(translated)


@register.simple_tag(takes_context=True)
def blocktrans(context, content, **kwargs):
    """
    Simple blocktrans implementation for basic HTML content translation.
    
    Usage:
        {% load i18n_compat %}
        {% blocktrans %}Hello <strong>World</strong>{% endblocktrans %}
    """
    current_lang = context.get('current_language_code', 'en')
    translated = get_translation(content, current_lang)
    return mark_safe(translated)


class TransNode(template.Node):
    """
    Node for handling {% trans %} template tag with more advanced features.
    """
    def __init__(self, message, asvar=None):
        self.message = message
        self.asvar = asvar

    def render(self, context):
        # Get current language from context
        current_lang = context.get('current_language_code', 'en')
        
        # Resolve message if it's a variable
        if hasattr(self.message, 'resolve'):
            message = self.message.resolve(context)
        else:
            message = self.message
        
        # Get translation
        translated = get_translation(str(message), current_lang)
        
        # Handle variable assignment
        if self.asvar:
            context[self.asvar] = translated
            return ''
        
        return translated


@register.tag('trans')
def do_trans(parser, token):
    """
    Parse {% trans %} template tag.
    
    Syntax:
        {% trans "message" %}
        {% trans variable %}
        {% trans "message" as variable %}
    """
    bits = token.split_contents()
    if len(bits) < 2:
        raise template.TemplateSyntaxError("'trans' tag requires at least one argument")
    
    message = parser.compile_filter(bits[1])
    asvar = None
    
    # Handle 'as variable' syntax
    if len(bits) == 4 and bits[2] == 'as':
        asvar = bits[3]
    elif len(bits) > 2:
        raise template.TemplateSyntaxError("'trans' tag syntax: {% trans 'message' %} or {% trans 'message' as variable %}")
    
    return TransNode(message, asvar)


# Alias for compatibility
register.tag('_', do_trans)


@register.filter
def translate(value, language='vi'):
    """
    Filter to translate a value.
    
    Usage:
        {{ "Hello World"|translate }}
        {{ "Hello World"|translate:"vi" }}
    """
    return get_translation(str(value), language)


@register.inclusion_tag('vocabulary/i18n_debug.html', takes_context=True)
def i18n_debug(context):
    """
    Debug tag to show current language and available translations.
    Only works in DEBUG mode.
    """
    from django.conf import settings
    
    if not settings.DEBUG:
        return {}
    
    return {
        'current_language': context.get('current_language_code', 'en'),
        'available_languages': ['en', 'vi'],
        'translation_count': len(get_translation.__defaults__[0] if hasattr(get_translation, '__defaults__') else {}),
    }
