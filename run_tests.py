#!/usr/bin/env python
"""
Enhanced test runner with professional visual output and simplified coverage reporting.
"""
import os
import sys
import time
import warnings

# SUPPRESS ALL RUNTIME WARNINGS IMMEDIATELY
warnings.filterwarnings('ignore', category=RuntimeWarning)
os.environ['PYTHONWARNINGS'] = 'ignore::RuntimeWarning'

import django
from django.conf import settings
from django.test.utils import get_runner
from django.test.runner import DiscoverRunner
from django.test import TestCase
from unittest.result import TestResult
from io import StringIO

class Colors:
    """ANSI color codes for terminal output."""
    GREEN = '\033[92m'
    RED = '\033[91m'
    YELLOW = '\033[93m'
    BLUE = '\033[94m'
    MAGENTA = '\033[95m'
    CYAN = '\033[96m'
    WHITE = '\033[97m'
    BOLD = '\033[1m'
    UNDERLINE = '\033[4m'
    RESET = '\033[0m'

    @classmethod
    def disable(cls):
        """Disable colors for non-terminal output."""
        cls.GREEN = cls.RED = cls.YELLOW = cls.BLUE = ''
        cls.MAGENTA = cls.CYAN = cls.WHITE = cls.BOLD = ''
        cls.UNDERLINE = cls.RESET = ''


class EnhancedTestResult(TestResult):
    """Enhanced test result class with visual indicators and timing."""

    def __init__(self, stream=None, verbosity=1):
        super().__init__()
        self.stream = stream or sys.stdout
        self.verbosity = verbosity
        self.test_results = []
        self.start_time = None
        self.current_test_start = None

    def startTest(self, test):
        super().startTest(test)
        self.current_test_start = time.time()
        if self.verbosity >= 2:
            test_name = self._get_test_name(test)
            self.stream.write(f"  {Colors.CYAN}Running:{Colors.RESET} {test_name} ... ")
            self.stream.flush()

    def addSuccess(self, test):
        super().addSuccess(test)
        duration = time.time() - self.current_test_start if self.current_test_start else 0
        test_name = self._get_test_name(test)

        self.test_results.append({
            'status': 'PASS',
            'name': test_name,
            'duration': duration,
            'error': None
        })

        if self.verbosity >= 2:
            self.stream.write(f"{Colors.GREEN}✓ PASS{Colors.RESET} ({duration:.3f}s)\n")
        else:
            self.stream.write(f"{Colors.GREEN}✓{Colors.RESET}")
        self.stream.flush()

    def addError(self, test, err):
        super().addError(test, err)
        duration = time.time() - self.current_test_start if self.current_test_start else 0
        test_name = self._get_test_name(test)

        self.test_results.append({
            'status': 'ERROR',
            'name': test_name,
            'duration': duration,
            'error': self._exc_info_to_string(err, test)
        })

        if self.verbosity >= 2:
            self.stream.write(f"{Colors.RED}✗ ERROR{Colors.RESET} ({duration:.3f}s)\n")
        else:
            self.stream.write(f"{Colors.RED}✗{Colors.RESET}")
        self.stream.flush()

    def addFailure(self, test, err):
        super().addFailure(test, err)
        duration = time.time() - self.current_test_start if self.current_test_start else 0
        test_name = self._get_test_name(test)

        self.test_results.append({
            'status': 'FAIL',
            'name': test_name,
            'duration': duration,
            'error': self._exc_info_to_string(err, test)
        })

        if self.verbosity >= 2:
            self.stream.write(f"{Colors.RED}✗ FAIL{Colors.RESET} ({duration:.3f}s)\n")
        else:
            self.stream.write(f"{Colors.RED}✗{Colors.RESET}")
        self.stream.flush()

    def addSkip(self, test, reason):
        super().addSkip(test, reason)
        duration = time.time() - self.current_test_start if self.current_test_start else 0
        test_name = self._get_test_name(test)

        self.test_results.append({
            'status': 'SKIP',
            'name': test_name,
            'duration': duration,
            'error': f"Skipped: {reason}"
        })

        if self.verbosity >= 2:
            self.stream.write(f"{Colors.YELLOW}⚠ SKIP{Colors.RESET} ({duration:.3f}s)\n")
        else:
            self.stream.write(f"{Colors.YELLOW}⚠{Colors.RESET}")
        self.stream.flush()

    def _get_test_name(self, test):
        """Extract a clean test name from the test object."""
        if hasattr(test, '_testMethodName'):
            class_name = test.__class__.__name__
            method_name = test._testMethodName
            module_name = test.__class__.__module__
            return f"{module_name}.{class_name}.{method_name}"
        return str(test)

class EnhancedTestRunner(DiscoverRunner):
    """Enhanced Django test runner with professional visual output."""

    def __init__(self, verbosity=1, interactive=False, **kwargs):
        super().__init__(verbosity=verbosity, interactive=interactive, **kwargs)
        self.start_time = None
        self.total_time = 0
        self.stream = sys.stdout

        # Disable colors if output is not a terminal
        if not sys.stdout.isatty():
            Colors.disable()

        # Apply comprehensive warning suppression
        self._suppress_all_warnings()

    def _suppress_all_warnings(self):
        """Suppress all expected warnings during testing."""
        import warnings

        # Suppress Django timezone warnings with multiple patterns
        warning_patterns = [
            'DateTimeField .* received a naive datetime',
            '.*received a naive datetime.*',
            '.*naive datetime.*',
            '.*DateTimeField.*',
            '.*time zone support is active.*'
        ]

        for pattern in warning_patterns:
            warnings.filterwarnings('ignore',
                                  message=pattern,
                                  category=RuntimeWarning)

        # Also suppress by module
        warnings.filterwarnings('ignore',
                              category=RuntimeWarning,
                              module='django.db.models.fields')

        warnings.filterwarnings('ignore',
                              category=RuntimeWarning,
                              module='django.db.models.fields.__init__')

    def setup_test_environment(self, **kwargs):
        super().setup_test_environment(**kwargs)
        self._suppress_all_warnings()  # Apply again before tests
        self._print_header()

    def run_tests(self, test_labels, **kwargs):
        """Run tests with enhanced visual output."""
        # Apply comprehensive warning suppression
        import warnings
        with warnings.catch_warnings():
            warnings.simplefilter("ignore", RuntimeWarning)

            self.start_time = time.time()
            result = super().run_tests(test_labels, **kwargs)
            self.total_time = time.time() - self.start_time
            return result

    def get_resultclass(self):
        """Return our enhanced result class."""
        return EnhancedTestResult

    def run_suite(self, suite, **kwargs):
        """Run the test suite with enhanced output."""
        # Apply warning suppression again before running tests
        self._suppress_all_warnings()

        # Monkey-patch warnings.warn to suppress RuntimeWarnings
        import warnings
        original_warn = warnings.warn

        def silent_warn(message, category=None, stacklevel=1, source=None):
            if category == RuntimeWarning and 'naive datetime' in str(message):
                return  # Suppress timezone warnings
            return original_warn(message, category, stacklevel, source)

        warnings.warn = silent_warn

        try:
            self._print_test_start(suite)

            # Create our enhanced result object
            result = EnhancedTestResult(stream=self.stream, verbosity=self.verbosity)

            # Run the tests
            start_time = time.time()
            suite.run(result)
            end_time = time.time()

            # Print summary
            self._print_summary(result, end_time - start_time)

            return result
        finally:
            # Restore original warnings.warn
            warnings.warn = original_warn

    def _print_header(self):
        """Print a professional header."""
        header = f"""
{Colors.BOLD}{Colors.BLUE}╔══════════════════════════════════════════════════════════════════════════════╗
║                           ENHANCED TEST RUNNER                              ║
║                         LearnEnglish Project Tests                          ║
╚══════════════════════════════════════════════════════════════════════════════╝{Colors.RESET}
"""
        self.stream.write(header)
        self.stream.flush()

    def _print_test_start(self, suite):
        """Print test execution start information."""
        test_count = suite.countTestCases()
        self.stream.write(f"\n{Colors.BOLD}🚀 Starting Test Execution{Colors.RESET}\n")
        self.stream.write(f"Found {Colors.BOLD}{test_count}{Colors.RESET} test(s).\n")
        self.stream.write(f"{Colors.CYAN}{'─' * 80}{Colors.RESET}\n")

        if self.verbosity < 2:
            self.stream.write(f"\n{Colors.BOLD}Test Progress:{Colors.RESET} ")
        else:
            self.stream.write(f"\n{Colors.BOLD}Detailed Test Results:{Colors.RESET}\n")
        self.stream.flush()

    def _print_summary(self, result, duration):
        """Print a comprehensive test summary."""
        total_tests = len(result.test_results)
        passed = len([r for r in result.test_results if r['status'] == 'PASS'])
        failed = len([r for r in result.test_results if r['status'] in ['FAIL', 'ERROR']])
        skipped = len([r for r in result.test_results if r['status'] == 'SKIP'])

        # Print newline if we were showing progress dots
        if self.verbosity < 2:
            self.stream.write("\n")

        # Summary header
        self.stream.write(f"\n{Colors.CYAN}{'─' * 80}{Colors.RESET}\n")
        self.stream.write(f"{Colors.BOLD}{Colors.WHITE}📊 TEST EXECUTION SUMMARY{Colors.RESET}\n")
        self.stream.write(f"{Colors.CYAN}{'─' * 80}{Colors.RESET}\n")

        # Statistics
        self.stream.write(f"\n{Colors.BOLD}Statistics:{Colors.RESET}\n")
        self.stream.write(f"  Total Tests:     {Colors.BOLD}{total_tests}{Colors.RESET}\n")
        self.stream.write(f"  Passed:          {Colors.GREEN}{Colors.BOLD}{passed}{Colors.RESET} {Colors.GREEN}✓{Colors.RESET}\n")

        if failed > 0:
            self.stream.write(f"  Failed:          {Colors.RED}{Colors.BOLD}{failed}{Colors.RESET} {Colors.RED}✗{Colors.RESET}\n")
        else:
            self.stream.write(f"  Failed:          {Colors.BOLD}{failed}{Colors.RESET}\n")

        if skipped > 0:
            self.stream.write(f"  Skipped:         {Colors.YELLOW}{Colors.BOLD}{skipped}{Colors.RESET} {Colors.YELLOW}⚠{Colors.RESET}\n")
        else:
            self.stream.write(f"  Skipped:         {Colors.BOLD}{skipped}{Colors.RESET}\n")

        # Timing information
        self.stream.write(f"\n{Colors.BOLD}Timing:{Colors.RESET}\n")
        self.stream.write(f"  Total Duration:  {Colors.BOLD}{duration:.3f}s{Colors.RESET}\n")
        if total_tests > 0:
            avg_time = duration / total_tests
            self.stream.write(f"  Average/Test:    {Colors.BOLD}{avg_time:.3f}s{Colors.RESET}\n")

        # Overall status
        if failed == 0:
            status_color = Colors.GREEN
            status_icon = "✅"
            status_text = "ALL TESTS PASSED"
        else:
            status_color = Colors.RED
            status_icon = "❌"
            status_text = "SOME TESTS FAILED"

        self.stream.write(f"\n{Colors.BOLD}Overall Status:{Colors.RESET}\n")
        self.stream.write(f"  {status_color}{Colors.BOLD}{status_icon} {status_text}{Colors.RESET}\n")

        # Show failed tests details
        if failed > 0:
            self._print_failure_details(result)

        # Footer
        self.stream.write(f"\n{Colors.CYAN}{'─' * 80}{Colors.RESET}\n")
        self.stream.flush()

    def _print_failure_details(self, result):
        """Print detailed information about failed tests."""
        failed_tests = [r for r in result.test_results if r['status'] in ['FAIL', 'ERROR']]

        if not failed_tests:
            return

        self.stream.write(f"\n{Colors.RED}{Colors.BOLD}❌ FAILED TESTS DETAILS{Colors.RESET}\n")
        self.stream.write(f"{Colors.RED}{'─' * 80}{Colors.RESET}\n")

        for i, test in enumerate(failed_tests, 1):
            self.stream.write(f"\n{Colors.RED}{Colors.BOLD}{i}. {test['name']}{Colors.RESET}\n")
            self.stream.write(f"   Status: {Colors.RED}{test['status']}{Colors.RESET}\n")
            self.stream.write(f"   Duration: {test['duration']:.3f}s\n")
            if test['error']:
                # Show first few lines of error
                error_lines = test['error'].split('\n')[:5]
                self.stream.write(f"   Error Preview:\n")
                for line in error_lines:
                    if line.strip():
                        self.stream.write(f"     {Colors.RED}{line}{Colors.RESET}\n")
                if len(test['error'].split('\n')) > 5:
                    self.stream.write(f"     {Colors.YELLOW}... (truncated){Colors.RESET}\n")

def setup_test_environment():
    """Set up Django test environment."""
    import warnings

    # AGGRESSIVE warning suppression - catch ALL RuntimeWarnings during tests
    warnings.filterwarnings('ignore', category=RuntimeWarning)

    # Also set environment variable to suppress Django warnings
    os.environ['PYTHONWARNINGS'] = 'ignore::RuntimeWarning'

    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'tests.test_settings')
    django.setup()


def get_file_coverage_status(cov, file_path, threshold=80):
    """Get simple pass/fail coverage status for a file."""
    try:
        analysis = cov.analysis2(file_path)
        if analysis:
            statements = len(analysis[1])  # Total statements
            missing = len(analysis[3])     # Missing statements
            if statements > 0:
                coverage_percent = ((statements - missing) / statements) * 100
                return coverage_percent >= threshold
        return True  # If no statements, consider it passing
    except:
        return True  # If analysis fails, consider it passing


def print_simple_coverage_report(cov, threshold=80):
    """Print simplified file-by-file coverage report."""
    print(f"\n{Colors.BOLD}{Colors.BLUE}📈 COVERAGE CHECK{Colors.RESET}")
    print(f"{Colors.BLUE}{'═' * 30}{Colors.RESET}")

    # Get all measured files
    measured_files = cov.get_data().measured_files()

    # Filter to only include our source files
    source_files = []
    for file_path in measured_files:
        if any(src in file_path for src in ['vocabulary', 'accounts', 'learn_english_project']):
            # Skip test files, migrations, etc.
            if not any(skip in file_path for skip in ['test', 'migration', '__pycache__', '.pyc']):
                source_files.append(file_path)

    if not source_files:
        print(f"{Colors.YELLOW}⚠ No source files found for coverage check{Colors.RESET}")
        return

    # Check each file and print status
    passed_files = 0
    total_files = len(source_files)

    for file_path in sorted(source_files):
        # Extract just the filename for display
        filename = os.path.basename(file_path)
        if filename.endswith('.py'):
            filename = filename[:-3]  # Remove .py extension

        is_passing = get_file_coverage_status(cov, file_path, threshold)

        if is_passing:
            print(f"{filename}: {Colors.GREEN}✓{Colors.RESET}")
            passed_files += 1
        else:
            print(f"{filename}: {Colors.RED}✗{Colors.RESET}")

    # Summary
    print(f"\n{Colors.BOLD}Coverage Summary:{Colors.RESET}")
    if passed_files == total_files:
        print(f"  {Colors.GREEN}✅ All files meet coverage threshold ({threshold}%){Colors.RESET}")
    else:
        failed_files = total_files - passed_files
        print(f"  {Colors.YELLOW}⚠ {failed_files}/{total_files} files below threshold ({threshold}%){Colors.RESET}")


def run_tests_with_coverage(test_labels=None, verbosity=1, **kwargs):
    """Run tests with enhanced visual output and simplified coverage reporting."""
    try:
        import coverage
    except ImportError:
        print(f"{Colors.YELLOW}⚠ Coverage.py not installed. Install with: pip install coverage{Colors.RESET}")
        return run_tests_without_coverage(test_labels, verbosity, **kwargs)

    # Start coverage
    cov = coverage.Coverage(
        source=['vocabulary', 'accounts', 'learn_english_project'],
        omit=[
            '*/migrations/*',
            '*/venv/*',
            '*/env/*',
            '*/tests/*',
            'manage.py',
            '*/settings/*',
            '*/wsgi.py',
            '*/asgi.py',
        ]
    )
    cov.start()

    try:
        # Run tests with enhanced runner
        result = run_tests_without_coverage(test_labels, verbosity, **kwargs)

        # Stop coverage and generate simplified report
        cov.stop()
        cov.save()

        # Print simplified coverage report
        print_simple_coverage_report(cov)

        # Generate HTML report silently
        try:
            cov.html_report(directory='htmlcov')
            print(f"\n{Colors.GREEN}✓{Colors.RESET} HTML coverage report generated in 'htmlcov' directory")
        except Exception:
            pass  # Silently ignore HTML report errors

        return result

    except Exception as e:
        cov.stop()
        print(f"{Colors.RED}✗ Error running tests with coverage:{Colors.RESET} {e}")
        return 1


def run_tests_without_coverage(test_labels=None, verbosity=1, **kwargs):
    """Run tests without coverage using enhanced visual output."""
    setup_test_environment()

    # Use our enhanced test runner
    test_runner = EnhancedTestRunner(verbosity=verbosity, interactive=False, **kwargs)

    failures = test_runner.run_tests(test_labels or [])
    return failures


def run_specific_test_suite(suite_name):
    """Run a specific test suite."""
    test_suites = {
        'models': ['tests.test_models_comprehensive'],
        'api': ['tests.test_api_services_comprehensive'],
        'views': ['tests.test_views_comprehensive'],
        'integration': ['tests.test_integration_workflows'],
        'performance': ['tests.test_performance'],
        'unit': [
            'tests.test_audio_service',
            'tests.test_statistics_utils',
            'tests.test_word_details_service',
            'tests.test_context_processors',
            'tests.test_management_commands',
            'tests.test_urls_and_admin'
        ],
        'all': [],
    }

    if suite_name not in test_suites:
        print(f"Unknown test suite: {suite_name}")
        print(f"Available suites: {', '.join(test_suites.keys())}")
        return 1

    test_labels = test_suites[suite_name]
    return run_tests_with_coverage(test_labels)


def main():
    """Main test runner function."""
    import argparse

    parser = argparse.ArgumentParser(description='Enhanced test runner for LearnEnglish app')
    parser.add_argument(
        'tests',
        nargs='*',
        help='Specific test modules to run (e.g., tests.test_models)'
    )
    parser.add_argument(
        '--no-coverage',
        action='store_true',
        help='Run tests without coverage reporting'
    )
    parser.add_argument(
        '--suite',
        choices=['models', 'api', 'views', 'integration', 'performance', 'unit', 'all'],
        help='Run a specific test suite'
    )
    parser.add_argument(
        '--verbosity',
        type=int,
        default=1,
        choices=[0, 1, 2],
        help='Verbosity level (0=minimal, 1=progress dots, 2=detailed)'
    )
    parser.add_argument(
        '--parallel',
        type=int,
        help='Run tests in parallel (number of processes)'
    )
    parser.add_argument(
        '--keepdb',
        action='store_true',
        help='Keep test database between runs'
    )
    parser.add_argument(
        '--debug-mode',
        action='store_true',
        help='Run tests in debug mode'
    )
    parser.add_argument(
        '--classic',
        action='store_true',
        help='Use classic Django test runner instead of enhanced'
    )
    parser.add_argument(
        '--coverage-threshold',
        type=int,
        default=80,
        help='Coverage threshold percentage (default: 80)'
    )

    args = parser.parse_args()

    # Set environment variables based on arguments
    if args.parallel:
        os.environ['DJANGO_TEST_PARALLEL'] = str(args.parallel)

    if args.keepdb:
        os.environ['DJANGO_TEST_KEEPDB'] = '1'

    if args.debug_mode:
        os.environ['DJANGO_TEST_DEBUG'] = '1'

    # Prepare kwargs for test runner
    runner_kwargs = {}
    if args.parallel:
        runner_kwargs['parallel'] = args.parallel
    if args.keepdb:
        runner_kwargs['keepdb'] = True

    # Run specific test suite
    if args.suite:
        return run_specific_test_suite(args.suite)

    # Use classic runner if requested
    if args.classic:
        setup_test_environment()
        TestRunner = get_runner(settings)
        test_runner = TestRunner(verbosity=args.verbosity, interactive=False, **runner_kwargs)
        failures = test_runner.run_tests(args.tests or [])
        return failures

    # Run tests with enhanced runner (default)
    if args.no_coverage:
        return run_tests_without_coverage(args.tests, args.verbosity, **runner_kwargs)
    else:
        return run_tests_with_coverage(args.tests, args.verbosity, **runner_kwargs)


if __name__ == '__main__':
    sys.exit(main())