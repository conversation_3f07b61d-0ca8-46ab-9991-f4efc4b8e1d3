#!/usr/bin/env python
"""
Test script for i18n migration functionality.
Tests the new template tags and translation system.
"""
import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'learn_english_project.settings')
django.setup()

from django.test import RequestFactory, Client
from django.contrib.auth import get_user_model
from django.template import Context, Template
from vocabulary.context_processors import get_translation, i18n_compatible_translations

User = get_user_model()

def test_translation_function():
    """Test the get_translation function."""
    print("🧪 Testing get_translation function...")
    
    # Test English (should return original)
    assert get_translation("Hello World", "en") == "Hello World"
    
    # Test Vietnamese translations
    assert get_translation("Learn English", "vi") == "Học Tiếng Anh"
    assert get_translation("Dashboard", "vi") == "Bảng điều khiển"
    assert get_translation("Save", "vi") == "Lưu"
    
    # Test non-existent translation (should return original)
    assert get_translation("Non-existent text", "vi") == "Non-existent text"
    
    print("✅ get_translation function works correctly!")

def test_context_processor():
    """Test the context processor."""
    print("🧪 Testing context processor...")
    
    factory = RequestFactory()
    request = factory.get('/')
    request.session = {'django_language': 'vi'}
    
    context = i18n_compatible_translations(request)
    
    # Check that all required keys are present
    assert 'manual_texts' in context
    assert 'current_language_code' in context
    assert 'js_translations_json' in context
    assert 'trans' in context
    assert 'get_translation' in context
    
    # Test the trans function
    trans_func = context['trans']
    assert trans_func("Learn English") == "Học Tiếng Anh"
    
    print("✅ Context processor works correctly!")

def test_template_tags():
    """Test the custom template tags."""
    print("🧪 Testing template tags...")
    
    # Create a template with our custom tags
    template_content = """
    {% load i18n_compat %}
    <h1>{% trans "Learn English" %}</h1>
    <p>{% trans "Dashboard" %}</p>
    <span>{{ "Save"|translate }}</span>
    """
    
    template = Template(template_content)
    
    # Create context with Vietnamese language
    context = Context({
        'current_language_code': 'vi'
    })
    
    rendered = template.render(context)
    
    # Check that translations are applied
    assert "Học Tiếng Anh" in rendered
    assert "Bảng điều khiển" in rendered
    assert "Lưu" in rendered
    
    print("✅ Template tags work correctly!")

def test_client_request():
    """Test with Django test client."""
    print("🧪 Testing with Django client...")
    
    client = Client()
    
    # Create a test user
    try:
        user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )
        print(f"Created test user: {user.email}")
    except:
        user = User.objects.get(email='<EMAIL>')
        print(f"Using existing test user: {user.email}")
    
    # Login
    client.login(email='<EMAIL>', password='testpass123')
    
    # Test the i18n test page
    response = client.get('/vi/i18n-test/')
    
    if response.status_code == 200:
        print("✅ i18n test page loads successfully!")
        
        # Check for Vietnamese translations in response
        content = response.content.decode('utf-8')
        if "Học Tiếng Anh" in content:
            print("✅ Vietnamese translations are working in templates!")
        else:
            print("❌ Vietnamese translations not found in template output")
    else:
        print(f"❌ i18n test page failed to load: {response.status_code}")
        print(f"Response: {response.content.decode('utf-8')[:500]}")

def main():
    """Run all tests."""
    print("🚀 Starting i18n migration tests...\n")
    
    try:
        test_translation_function()
        print()
        
        test_context_processor()
        print()
        
        test_template_tags()
        print()
        
        test_client_request()
        print()
        
        print("🎉 All tests passed! i18n migration system is working correctly.")
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0

if __name__ == '__main__':
    sys.exit(main())
